import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:printing/printing.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_report_model.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/presentation/widgets/irrigation_list_item.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/services/irrigation_report_service.dart';
import 'package:untitled/services/pdf_service.dart';

/// صفحة تقرير التسقيات المتقدمة
class IrrigationReportsPage extends StatefulWidget {
  const IrrigationReportsPage({super.key});

  @override
  State<IrrigationReportsPage> createState() => _IrrigationReportsPageState();
}

class _IrrigationReportsPageState extends State<IrrigationReportsPage> {
  List<IrrigationModel> _irrigations = [];
  List<ClientModel> _clients = [];
  List<FarmModel> _farms = [];
  
  bool _isLoading = false;
  int _loadedCount = 0;
  final int _totalDataSources = 3;
  
  // فلاتر
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedClient = 'all';
  String _selectedFarm = 'all';
  String _selectedStatus = 'all'; // all, completed, in_progress, cancelled
  String _sortBy = 'date'; // date, cost, duration, diesel
  bool _sortAscending = false;
  
  // بحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadAllData() {
    setState(() {
      _isLoading = true;
      _loadedCount = 0;
    });
    
    context.read<IrrigationBloc>().add(const LoadIrrigations());
    context.read<ClientBloc>().add(const LoadClients());
    context.read<FarmBloc>().add(const LoadFarms());
  }

  void _checkDataLoaded() {
    _loadedCount++;
    if (_loadedCount >= _totalDataSources) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<IrrigationModel> get _filteredIrrigations {
    var filtered = _irrigations.where((irrigation) {
      // فلتر التاريخ
      final startOfDay = DateTime(_startDate.year, _startDate.month, _startDate.day);
      final endOfDay = DateTime(_endDate.year, _endDate.month, _endDate.day, 23, 59, 59);
      
      if (irrigation.startTime.isBefore(startOfDay) || irrigation.startTime.isAfter(endOfDay)) {
        return false;
      }
      
      // فلتر البحث
      if (_searchQuery.isNotEmpty) {
        final client = _getClientById(irrigation.clientId);
        final farm = _getFarmById(irrigation.farmId);
        if (!(client?.name.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) &&
            !(farm?.name.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) &&
            !(irrigation.notes?.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false)) {
          return false;
        }
      }
      
      // فلتر العميل
      if (_selectedClient != 'all' && irrigation.clientId.toString() != _selectedClient) {
        return false;
      }
      
      // فلتر المزرعة
      if (_selectedFarm != 'all' && irrigation.farmId.toString() != _selectedFarm) {
        return false;
      }
      
      // فلتر الحالة
      if (_selectedStatus != 'all') {
        // يمكن إضافة منطق فلترة الحالة هنا
      }
      
      return true;
    }).toList();
    
    // ترتيب النتائج
    filtered.sort((a, b) {
      int result = 0;
      switch (_sortBy) {
        case 'date':
          result = a.startTime.compareTo(b.startTime);
          break;
        case 'cost':
          result = a.cost.compareTo(b.cost);
          break;
        case 'duration':
          result = a.duration.compareTo(b.duration);
          break;
        case 'diesel':
          result = a.dieselConsumption.compareTo(b.dieselConsumption);
          break;
      }
      return _sortAscending ? result : -result;
    });
    
    return filtered;
  }

  ClientModel? _getClientById(int clientId) {
    try {
      return _clients.firstWhere((client) => client.id == clientId);
    } catch (e) {
      return null;
    }
  }

  FarmModel? _getFarmById(int farmId) {
    try {
      return _farms.firstWhere((farm) => farm.id == farmId);
    } catch (e) {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: MultiBlocListener(
              listeners: _buildBlocListeners(),
              child: _isLoading
                  ? _buildLoadingWidget()
                  : Column(
                      children: [
                        _buildFiltersSection(),
                        _buildSearchBar(),
                        _buildSortingSection(),
                        Expanded(
                          child: _filteredIrrigations.isEmpty
                              ? _buildEmptyState()
                              : _buildIrrigationsList(),
                        ),
                      ],
                    ),
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'تقرير التسقيات',
        style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
      ),
      backgroundColor: AppTheme.primaryColor,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadAllData,
          tooltip: 'تحديث البيانات',
        ),
        IconButton(
          icon: const Icon(Icons.file_download),
          onPressed: _exportToExcel,
          tooltip: 'تصدير إلى Excel',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'print':
                _printReport();
                break;
              case 'share':
                _shareReport();
                break;
              case 'analytics':
                _showAnalytics();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'print',
              child: Row(
                children: [
                  Icon(Icons.print),
                  SizedBox(width: 8),
                  Text('طباعة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('مشاركة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'analytics',
              child: Row(
                children: [
                  Icon(Icons.analytics),
                  SizedBox(width: 8),
                  Text('تحليلات متقدمة'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  List<BlocListener> _buildBlocListeners() {
    return [
      BlocListener<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          if (state is IrrigationsLoaded) {
            setState(() => _irrigations = state.irrigations);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<ClientBloc, ClientState>(
        listener: (context, state) {
          if (state is ClientsLoaded) {
            setState(() => _clients = state.clients);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<FarmBloc, FarmState>(
        listener: (context, state) {
          if (state is FarmsLoaded) {
            setState(() => _farms = state.farms);
            _checkDataLoaded();
          }
        },
      ),
    ];
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل بيانات التسقيات...',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'تم تحميل $_loadedCount من $_totalDataSources',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'فلترة التقرير',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDateRangeSelector(
                  label: 'من تاريخ',
                  date: _startDate,
                  onTap: () => _selectDate(context, true),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDateRangeSelector(
                  label: 'إلى تاريخ',
                  date: _endDate,
                  onTap: () => _selectDate(context, false),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDropdownFilter(
                  label: 'العميل',
                  value: _selectedClient,
                  items: [
                    const DropdownMenuItem(
                      value: 'all',
                      child: Text('جميع العملاء'),
                    ),
                    ..._clients.map((client) => DropdownMenuItem(
                          value: client.id.toString(),
                          child: Text(client.name),
                        )),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedClient = value;
                        // إعادة ضبط فلتر المزرعة إذا تم تغيير العميل
                        if (value != 'all') {
                          _selectedFarm = 'all';
                        }
                      });
                    }
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDropdownFilter(
                  label: 'المزرعة',
                  value: _selectedFarm,
                  items: [
                    const DropdownMenuItem(
                      value: 'all',
                      child: Text('جميع المزارع'),
                    ),
                    ..._getFilteredFarms().map((farm) => DropdownMenuItem(
                          value: farm.id.toString(),
                          child: Text(farm.name),
                        )),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedFarm = value;
                      });
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  List<FarmModel> _getFilteredFarms() {
    if (_selectedClient == 'all') {
      return _farms;
    } else {
      final clientId = int.tryParse(_selectedClient);
      return _farms.where((farm) => farm.clientId == clientId).toList();
    }
  }

  Widget _buildDateRangeSelector({
    required String label,
    required DateTime date,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.withOpacity(0.3)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 16),
                const SizedBox(width: 8),
                Text(
                  DateFormat('yyyy/MM/dd').format(date),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdownFilter<T>({
    required String label,
    required T value,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(height: 4),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.withOpacity(0.3)),
          ),
          child: DropdownButton<T>(
            value: value,
            items: items,
            onChanged: onChanged,
            isExpanded: true,
            underline: const SizedBox(),
            icon: const Icon(Icons.arrow_drop_down),
          ),
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'بحث عن عميل، مزرعة، أو ملاحظات...',
          border: InputBorder.none,
          icon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    setState(() {
                      _searchController.clear();
                      _searchQuery = '';
                    });
                  },
                )
              : null,
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildSortingSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Text(
            'ترتيب حسب:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildSortButton('التاريخ', 'date'),
                  _buildSortButton('التكلفة', 'cost'),
                  _buildSortButton('المدة', 'duration'),
                  _buildSortButton('الديزل', 'diesel'),
                ],
              ),
            ),
          ),
          IconButton(
            icon: Icon(_sortAscending ? Icons.arrow_upward : Icons.arrow_downward),
            onPressed: () {
              setState(() {
                _sortAscending = !_sortAscending;
              });
            },
            tooltip: _sortAscending ? 'ترتيب تصاعدي' : 'ترتيب تنازلي',
          ),
        ],
      ),
    );
  }

  Widget _buildSortButton(String label, String value) {
    final isSelected = _sortBy == value;
    final color = isSelected ? AppTheme.primaryColor : Colors.grey;
    
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: InkWell(
        onTap: () {
          setState(() {
            _sortBy = value;
          });
        },
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: color.withOpacity(0.3)),
          ),
          child: Row(
            children: [
              Text(
                label,
                style: TextStyle(
                  color: color,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
              if (isSelected) ...[
                const SizedBox(width: 4),
                Icon(
                  _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                  size: 16,
                  color: color,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.orange.withOpacity(0.3)),
            ),
            child: const Icon(
              Icons.water_drop,
              size: 64,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'لا توجد تسقيات',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: 300,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'لا توجد تسقيات تطابق معايير البحث والفلترة المحددة. يرجى تغيير المعايير أو إضافة تسقيات جديدة.',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey.shade600,
              ),
            ),
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: _loadAllData,
            icon: const Icon(Icons.refresh),
            label: const Text('تحديث البيانات'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIrrigationsList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _filteredIrrigations.length,
      itemBuilder: (context, index) {
        final irrigation = _filteredIrrigations[index];
        final client = _getClientById(irrigation.clientId);
        final farm = _getFarmById(irrigation.farmId);
        
        return IrrigationListItem(
          irrigation: irrigation,
          clientName: client?.name ?? 'عميل غير معروف',
          farmName: farm?.name ?? 'مزرعة غير معروفة',
          onTap: () => _showIrrigationDetails(irrigation),
        );
      },
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton(
          heroTag: "analytics",
          onPressed: _showAnalytics,
          backgroundColor: Colors.purple,
          tooltip: 'تحليلات متقدمة',
          child: const Icon(Icons.analytics, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "export",
          onPressed: _exportToExcel,
          backgroundColor: Colors.green,
          tooltip: 'تصدير إلى Excel',
          child: const Icon(Icons.file_download, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "print",
          onPressed: _printReport,
          backgroundColor: Colors.blue,
          tooltip: 'طباعة التقرير',
          child: const Icon(Icons.print, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "share",
          onPressed: _shareReport,
          backgroundColor: Colors.orange,
          tooltip: 'مشاركة التقرير',
          child: const Icon(Icons.share, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "refresh",
          onPressed: _loadAllData,
          backgroundColor: AppTheme.primaryColor,
          tooltip: 'تحديث البيانات',
          child: const Icon(Icons.refresh, color: Colors.white),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          // تأكد من أن تاريخ البداية لا يتجاوز تاريخ النهاية
          if (_startDate.isAfter(_endDate)) {
            _endDate = _startDate;
          }
        } else {
          _endDate = picked;
          // تأكد من أن تاريخ النهاية لا يسبق تاريخ البداية
          if (_endDate.isBefore(_startDate)) {
            _startDate = _endDate;
          }
        }
      });
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة متقدمة'),
        content: const Text('سيتم تطوير خيارات الفلترة المتقدمة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showAnalytics() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('التحليلات المتقدمة'),
        content: const Text('سيتم تطوير التحليلات المتقدمة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  Future<void> _exportToExcel() async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير التقرير إلى Excel...'),
        backgroundColor: Colors.green,
      ),
    );
    // يمكن إضافة وظيفة التصدير إلى Excel لاحقاً
  }

  Future<void> _printReport() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري إنشاء تقرير PDF...'),
          backgroundColor: AppTheme.primaryColor,
        ),
      );
      
      final report = await _generateIrrigationReport();
      final pdfFile = await PdfService().createIrrigationReportPdf(
        report: report,
        logoAssetPath: 'assets/images/app_logo.png',
        clients: _clients,
        farms: _farms,
      );
      
      await Printing.layoutPdf(
        onLayout: (_) async => await pdfFile.readAsBytes(),
        name: 'تقرير التسقيات.pdf',
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء إنشاء التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _shareReport() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري إنشاء تقرير PDF للمشاركة...'),
          backgroundColor: Colors.blue,
        ),
      );
      
      final report = await _generateIrrigationReport();
      final pdfFile = await PdfService().createIrrigationReportPdf(
        report: report,
        logoAssetPath: 'assets/images/app_logo.png',
        clients: _clients,
        farms: _farms,
      );
      
      await Printing.sharePdf(
        bytes: await pdfFile.readAsBytes(),
        filename: 'تقرير التسقيات.pdf',
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء مشاركة التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  /// إنشاء تقرير التسقيات
  Future<IrrigationReportModel> _generateIrrigationReport() async {
    // الحصول على التسقيات المفلترة
    final filteredIrrigations = _filteredIrrigations;
    
    // إنشاء التقرير باستخدام الخدمة
    final reportService = IrrigationReportService();
    final report = reportService.generateIrrigationReport(
      irrigations: filteredIrrigations,
      clients: _clients,
      farms: _farms,
      fromDate: _startDate,
      toDate: _endDate,
      clientId: _selectedClient == 'all' ? null : _selectedClient,
      farmId: _selectedFarm == 'all' ? null : _selectedFarm,
    );
    
    return report;
  }

  /// عرض تفاصيل التسقية
  void _showIrrigationDetails(IrrigationModel irrigation) {
    final client = _getClientById(irrigation.clientId);
    final farm = _getFarmById(irrigation.farmId);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل التسقية - ${farm?.name ?? 'مزرعة غير معروفة'}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailItem('العميل', client?.name ?? 'عميل غير معروف'),
              _buildDetailItem('المزرعة', farm?.name ?? 'مزرعة غير معروفة'),
              _buildDetailItem('تاريخ البدء', DateFormat('yyyy/MM/dd HH:mm').format(irrigation.startTime)),
              _buildDetailItem('تاريخ الانتهاء', DateFormat('yyyy/MM/dd HH:mm').format(irrigation.endTime)),
              _buildDetailItem('المدة', '${irrigation.duration} دقيقة'),
              _buildDetailItem('استهلاك الديزل', '${irrigation.dieselConsumption.toStringAsFixed(2)} لتر'),
              _buildDetailItem('التكلفة', '${irrigation.cost.toStringAsFixed(2)} ريال'),
              if (irrigation.notes != null && irrigation.notes!.isNotEmpty)
                _buildDetailItem('ملاحظات', irrigation.notes!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Divider(),
        ],
      ),
    );
  }
}