import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:printing/printing.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_report_model.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/presentation/widgets/irrigation_list_item.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/services/irrigation_report_service.dart';
import 'package:untitled/services/pdf_service.dart';

/// صفحة تقرير التسقيات المتقدمة
class IrrigationReportsPage extends StatefulWidget {
  const IrrigationReportsPage({super.key});

  @override
  State<IrrigationReportsPage> createState() => _IrrigationReportsPageState();
}

class _IrrigationReportsPageState extends State<IrrigationReportsPage> {
  List<IrrigationModel> _irrigations = [];
  List<ClientModel> _clients = [];
  List<FarmModel> _farms = [];
  
  bool _isLoading = false;
  int _loadedCount = 0;
  final int _totalDataSources = 3;
  
  // فلاتر
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedClient = 'all';
  String _selectedFarm = 'all';
  String _selectedStatus = 'all'; // all, completed, in_progress, cancelled
  String _sortBy = 'date'; // date, cost, duration, diesel
  bool _sortAscending = false;
  
  // بحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadAllData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadAllData() {
    setState(() {
      _isLoading = true;
      _loadedCount = 0;
    });
    
    context.read<IrrigationBloc>().add(const LoadIrrigations());
    context.read<ClientBloc>().add(const LoadClients());
    context.read<FarmBloc>().add(const LoadFarms());
  }

  void _checkDataLoaded() {
    _loadedCount++;
    if (_loadedCount >= _totalDataSources) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<IrrigationModel> get _filteredIrrigations {
    var filtered = _irrigations.where((irrigation) {
      // فلتر التاريخ
      final startOfDay = DateTime(_startDate.year, _startDate.month, _startDate.day);
      final endOfDay = DateTime(_endDate.year, _endDate.month, _endDate.day, 23, 59, 59);
      
      if (irrigation.startTime.isBefore(startOfDay) || irrigation.startTime.isAfter(endOfDay)) {
        return false;
      }
      
      // فلتر البحث
      if (_searchQuery.isNotEmpty) {
        final client = _getClientById(irrigation.clientId);
        final farm = _getFarmById(irrigation.farmId);
        if (!(client?.name.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false) &&
            !(farm?.name.toLowerCase().contains(_searchQuery.toLowerCase()) ?? false)) {
          return false;
        }
      }
      
      // فلتر العميل
      if (_selectedClient != 'all' && irrigation.clientId.toString() != _selectedClient) {
        return false;
      }
      
      // فلتر المزرعة
      if (_selectedFarm != 'all' && irrigation.farmId.toString() != _selectedFarm) {
        return false;
      }
      
      // فلتر الحالة
      if (_selectedStatus != 'all') {
        final status = _getIrrigationStatus(irrigation);
        if (status != _selectedStatus) {
          return false;
        }
      }
      
      return true;
    }).toList();

    // ترتيب
    filtered.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'date':
          comparison = a.startTime.compareTo(b.startTime);
          break;
        case 'cost':
          comparison = a.cost.compareTo(b.cost);
          break;
        case 'duration':
          comparison = a.duration.compareTo(b.duration);
          break;
        case 'diesel':
          comparison = a.dieselConsumption.compareTo(b.dieselConsumption);
          break;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  ClientModel? _getClientById(int clientId) {
    try {
      return _clients.firstWhere((client) => client.id == clientId);
    } catch (e) {
      debugPrint('⚠️ لم يتم العثور على العميل بالمعرف: $clientId');
      return null;
    }
  }

  FarmModel? _getFarmById(int farmId) {
    try {
      return _farms.firstWhere((farm) => farm.id == farmId);
    } catch (e) {
      debugPrint('⚠️ لم يتم العثور على المزرعة بالمعرف: $farmId');
      // محاولة البحث بمقارنة النص للتأكد
      try {
        return _farms.firstWhere((farm) => farm.id.toString() == farmId.toString());
      } catch (e2) {
        debugPrint('⚠️ فشل في العثور على المزرعة حتى بمقارنة النص: $farmId');
        return null;
      }
    }
  }

  String _getIrrigationStatus(IrrigationModel irrigation) {
    final now = DateTime.now();
    final endTime = irrigation.startTime.add(Duration(minutes: irrigation.duration));
    
    if (endTime.isBefore(now)) {
      return 'completed';
    } else if (irrigation.startTime.isBefore(now) && endTime.isAfter(now)) {
      return 'in_progress';
    } else {
      return 'scheduled';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: MultiBlocListener(
        listeners: _buildBlocListeners(),
        child: _isLoading
            ? _buildLoadingWidget()
            : SingleChildScrollView(
                child: Column(
                  children: [
                    _buildFiltersSection(),
                    _buildSummaryCards(),
                    _buildChartsSection(),
                    _buildIrrigationsList(),
                    const SizedBox(height: 80), // مساحة إضافية لتجنب تداخل FloatingActionButton
                  ],
                ),
              ),
      ),
      floatingActionButton: _buildFloatingActionButtons(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'تقرير التسقيات',
        style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
      ),
      backgroundColor: AppTheme.primaryColor,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadAllData,
          tooltip: 'تحديث البيانات',
        ),
        IconButton(
          icon: const Icon(Icons.file_download),
          onPressed: _exportToExcel,
          tooltip: 'تصدير إلى Excel',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) {
            switch (value) {
              case 'print':
                _printReport();
                break;
              case 'share':
                _shareReport();
                break;
              case 'analytics':
                _showAnalytics();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'print',
              child: Row(
                children: [
                  Icon(Icons.print),
                  SizedBox(width: 8),
                  Text('طباعة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'share',
              child: Row(
                children: [
                  Icon(Icons.share),
                  SizedBox(width: 8),
                  Text('مشاركة التقرير'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'analytics',
              child: Row(
                children: [
                  Icon(Icons.analytics),
                  SizedBox(width: 8),
                  Text('تحليلات متقدمة'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  List<BlocListener> _buildBlocListeners() {
    return [
      BlocListener<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          if (state is IrrigationsLoaded) {
            setState(() => _irrigations = state.irrigations);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<ClientBloc, ClientState>(
        listener: (context, state) {
          if (state is ClientsLoaded) {
            setState(() => _clients = state.clients);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<FarmBloc, FarmState>(
        listener: (context, state) {
          if (state is FarmsLoaded) {
            setState(() => _farms = state.farms);
            _checkDataLoaded();
          }
        },
      ),
    ];
  }

  Widget _buildLoadingWidget() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل بيانات التسقيات...',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 8),
          Text(
            'تم تحميل $_loadedCount من $_totalDataSources',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.filter_list, color: AppTheme.primaryColor),
              const SizedBox(width: 8),
              const Text(
                'فلاتر التقرير',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              TextButton(
                onPressed: _resetFilters,
                child: const Text('إعادة تعيين'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث بالعميل أو المزرعة...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey.shade300),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.primaryColor),
              ),
            ),
            onChanged: (value) {
              setState(() => _searchQuery = value);
            },
          ),
          
          const SizedBox(height: 16),
          
          // فلاتر الفترة الزمنية
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('من تاريخ:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    InkWell(
                      onTap: () => _selectDate(context, true),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('yyyy-MM-dd').format(_startDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('إلى تاريخ:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    InkWell(
                      onTap: () => _selectDate(context, false),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today, size: 16),
                            const SizedBox(width: 8),
                            Text(DateFormat('yyyy-MM-dd').format(_endDate)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // فلاتر العميل والمزرعة
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('العميل:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<String>(
                      value: _selectedClient,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: [
                        const DropdownMenuItem(value: 'all', child: Text('جميع العملاء')),
                        ..._clients.map((client) => DropdownMenuItem(
                          value: client.id.toString(),
                          child: Text(client.name),
                        )),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedClient = value!);
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('المزرعة:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<String>(
                      value: _selectedFarm,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: [
                        const DropdownMenuItem(value: 'all', child: Text('جميع المزارع')),
                        ..._farms.map((farm) => DropdownMenuItem(
                          value: farm.id.toString(),
                          child: Text(farm.name),
                        )),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedFarm = value!);
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // فلاتر الحالة والترتيب
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('الحالة:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    DropdownButtonFormField<String>(
                      value: _selectedStatus,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: const [
                        DropdownMenuItem(value: 'all', child: Text('جميع الحالات')),
                        DropdownMenuItem(value: 'completed', child: Text('مكتملة')),
                        DropdownMenuItem(value: 'in_progress', child: Text('جارية')),
                        DropdownMenuItem(value: 'scheduled', child: Text('مجدولة')),
                      ],
                      onChanged: (value) {
                        setState(() => _selectedStatus = value!);
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('ترتيب حسب:', style: TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Expanded(
                          child: DropdownButtonFormField<String>(
                            value: _sortBy,
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            ),
                            items: const [
                              DropdownMenuItem(value: 'date', child: Text('التاريخ')),
                              DropdownMenuItem(value: 'cost', child: Text('التكلفة')),
                              DropdownMenuItem(value: 'duration', child: Text('المدة')),
                              DropdownMenuItem(value: 'diesel', child: Text('الديزل')),
                            ],
                            onChanged: (value) {
                              setState(() => _sortBy = value!);
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        IconButton(
                          icon: Icon(
                            _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                            color: AppTheme.primaryColor,
                          ),
                          onPressed: () {
                            setState(() => _sortAscending = !_sortAscending);
                          },
                          tooltip: _sortAscending ? 'تصاعدي' : 'تنازلي',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    final filteredIrrigations = _filteredIrrigations;
    double totalCost = filteredIrrigations.fold(0.0, (sum, irrigation) => sum + irrigation.cost);
    double totalDiesel = filteredIrrigations.fold(0.0, (sum, irrigation) => sum + irrigation.dieselConsumption);
    int totalDuration = filteredIrrigations.fold(0, (sum, irrigation) => sum + irrigation.duration);
    double avgCostPerHour = totalDuration > 0 ? totalCost / (totalDuration / 60) : 0.0;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ملخص التسقيات',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.5,
            children: [
              _buildSummaryCard(
                'إجمالي التسقيات',
                '${filteredIrrigations.length}',
                Icons.water_drop,
                Colors.blue,
                subtitle: 'للفترة المحددة',
              ),
              _buildSummaryCard(
                'إجمالي التكلفة',
                '${totalCost.toStringAsFixed(0)} ريال',
                Icons.attach_money,
                Colors.green,
              ),
              _buildSummaryCard(
                'إجمالي الديزل',
                '${totalDiesel.toStringAsFixed(0)} لتر',
                Icons.local_gas_station,
                Colors.orange,
              ),
              _buildSummaryCard(
                'متوسط التكلفة/ساعة',
                '${avgCostPerHour.toStringAsFixed(0)} ريال',
                Icons.schedule,
                Colors.purple,
                subtitle: '${(totalDuration / 60).toStringAsFixed(1)} ساعة إجمالي',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildChartsSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الرسوم البيانية',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildMiniChart(
                  'التسقيات اليومية',
                  _getDailyIrrigationsChart(),
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMiniChart(
                  'التكلفة اليومية',
                  _getDailyCostChart(),
                  Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMiniChart(String title, Widget chart, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(height: 60, child: chart),
        ],
      ),
    );
  }

  Widget _getDailyIrrigationsChart() {
    // رسم بياني بسيط للتسقيات اليومية
    final dailyData = <DateTime, int>{};
    for (final irrigation in _filteredIrrigations) {
      final date = DateTime(
        irrigation.startTime.year,
        irrigation.startTime.month,
        irrigation.startTime.day,
      );
      dailyData[date] = (dailyData[date] ?? 0) + 1;
    }

    return const Center(
      child: Text(
        'رسم بياني للتسقيات اليومية\n(سيتم تطويره)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 10, color: Colors.grey),
      ),
    );
  }

  Widget _getDailyCostChart() {
    // رسم بياني بسيط للتكلفة اليومية
    final dailyData = <DateTime, double>{};
    for (final irrigation in _filteredIrrigations) {
      final date = DateTime(
        irrigation.startTime.year,
        irrigation.startTime.month,
        irrigation.startTime.day,
      );
      dailyData[date] = (dailyData[date] ?? 0.0) + irrigation.cost;
    }

    return const Center(
      child: Text(
        'رسم بياني للتكلفة اليومية\n(سيتم تطويره)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 10, color: Colors.grey),
      ),
    );
  }

  Widget _buildIrrigationsList() {
    final filteredIrrigations = _filteredIrrigations;
    
    if (filteredIrrigations.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header section
        Container(
          margin: const EdgeInsets.fromLTRB(16, 0, 16, 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'قائمة التسقيات (${filteredIrrigations.length})',
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.view_list),
                    onPressed: () {
                      // تغيير طريقة العرض
                    },
                    tooltip: 'عرض قائمة',
                  ),
                  IconButton(
                    icon: const Icon(Icons.view_module),
                    onPressed: () {
                      // تغيير طريقة العرض
                    },
                    tooltip: 'عرض بطاقات',
                  ),
                ],
              ),
            ],
          ),
        ),
        
        // List section with enhanced irrigation items
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 8),
          itemCount: filteredIrrigations.length,
          itemBuilder: (context, index) {
            final irrigation = filteredIrrigations[index];
            final client = _getClientById(irrigation.clientId);
            final farm = _getFarmById(irrigation.farmId);
            
            return IrrigationListItem(
              irrigation: irrigation,
              clientName: client?.name,
              farmName: farm?.name,
              onTap: () => _showIrrigationDetails(irrigation),
              onEdit: null, // لا نحتاج التعديل في التقارير
              onDelete: null, // لا نحتاج الحذف في التقارير
            );
          },
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد تسقيات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'جرب تغيير معايير البحث أو الفلاتر',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _resetFilters,
              child: const Text('إعادة تعيين الفلاتر'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButtons() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        FloatingActionButton(
          heroTag: "analytics",
          onPressed: _showAnalytics,
          backgroundColor: Colors.purple,
          tooltip: 'تحليلات متقدمة',
          child: const Icon(Icons.analytics, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "export",
          onPressed: _exportToExcel,
          backgroundColor: Colors.green,
          tooltip: 'تصدير إلى Excel',
          child: const Icon(Icons.file_download, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "print",
          onPressed: _printReport,
          backgroundColor: Colors.blue,
          tooltip: 'طباعة التقرير',
          child: const Icon(Icons.print, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "share",
          onPressed: _shareReport,
          backgroundColor: Colors.orange,
          tooltip: 'مشاركة التقرير',
          child: const Icon(Icons.share, color: Colors.white),
        ),
        const SizedBox(height: 8),
        FloatingActionButton(
          heroTag: "refresh",
          onPressed: _loadAllData,
          backgroundColor: AppTheme.primaryColor,
          tooltip: 'تحديث البيانات',
          child: const Icon(Icons.refresh, color: Colors.white),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate ? _startDate : _endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          // التأكد من أن تاريخ البداية قبل تاريخ النهاية
          if (_endDate.isBefore(_startDate)) {
            _endDate = _startDate.add(const Duration(days: 1));
          }
        } else {
          // التأكد من أن تاريخ النهاية بعد تاريخ البداية
          if (picked.isBefore(_startDate)) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تاريخ النهاية يجب أن يكون بعد تاريخ البداية'),
                backgroundColor: Colors.orange,
              ),
            );
            return;
          }
          _endDate = picked;
        }
      });
    }
  }

  void _resetFilters() {
    setState(() {
      _startDate = DateTime.now().subtract(const Duration(days: 30));
      _endDate = DateTime.now();
      _selectedClient = 'all';
      _selectedFarm = 'all';
      _selectedStatus = 'all';
      _sortBy = 'date';
      _sortAscending = false;
      _searchController.clear();
      _searchQuery = '';
    });
  }

  void _showAnalytics() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('التحليلات المتقدمة'),
        content: const Text('سيتم تطوير التحليلات المتقدمة قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  Future<void> _exportToExcel() async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري تصدير التقرير إلى Excel...'),
        backgroundColor: Colors.green,
      ),
    );
    // يمكن إضافة وظيفة التصدير إلى Excel لاحقاً
  }

  Future<void> _printReport() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري إنشاء تقرير PDF...'),
          backgroundColor: AppTheme.primaryColor,
        ),
      );
      
      final report = await _generateIrrigationReport();
      final pdfFile = await PdfService().createIrrigationReportPdf2(
        report: report,
        logoAssetPath: 'assets/images/app_logo.png',
        clients: _clients,
        farms: _farms,
      );
      
      await Printing.layoutPdf(
        onLayout: (_) async => await pdfFile.readAsBytes(),
        name: 'تقرير التسقيات.pdf',
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء إنشاء التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _shareReport() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري إنشاء تقرير PDF للمشاركة...'),
          backgroundColor: Colors.blue,
        ),
      );
      
      final report = await _generateIrrigationReport();
      final pdfFile = await PdfService().createIrrigationReportPdf2(
        report: report,
        logoAssetPath: 'assets/images/app_logo.png',
        clients: _clients,
        farms: _farms,
      );
      
      await Printing.sharePdf(
        bytes: await pdfFile.readAsBytes(),
        filename: 'تقرير التسقيات.pdf',
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء مشاركة التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
  
  /// إنشاء تقرير التسقيات
  Future<IrrigationReportModel> _generateIrrigationReport() async {
    // الحصول على التسقيات المفلترة
    final filteredIrrigations = _filteredIrrigations;
    
    // إنشاء التقرير باستخدام الخدمة
    final reportService = IrrigationReportService();
    final report = reportService.generateIrrigationReport(
      irrigations: filteredIrrigations,
      clients: _clients,
      farms: _farms,
      fromDate: _startDate,
      toDate: _endDate,
      clientId: _selectedClient == 'all' ? null : _selectedClient,
      farmId: _selectedFarm == 'all' ? null : _selectedFarm,
    );
    
    return report;
  }

  /// عرض تفاصيل التسقية
  void _showIrrigationDetails(IrrigationModel irrigation) {
    final client = _getClientById(irrigation.clientId);
    final farm = _getFarmById(irrigation.farmId);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              irrigation.dateStatusIcon,
              color: irrigation.dateStatusColor,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text('تفاصيل التسقية #${irrigation.id}'),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات أساسية
              _buildDetailRow('العميل', client?.name ?? 'غير محدد'),
              _buildDetailRow('المزرعة', farm?.name ?? 'غير محددة'),
              const Divider(),
              
              // معلومات التوقيت
              _buildDetailRow('تاريخ البداية', DateFormat('yyyy/MM/dd HH:mm').format(irrigation.startTime)),
              _buildDetailRow('تاريخ النهاية', DateFormat('yyyy/MM/dd HH:mm').format(irrigation.endTime)),
              _buildDetailRow('المدة', '${irrigation.duration} دقيقة (${(irrigation.duration / 60).toStringAsFixed(1)} ساعة)'),
              const Divider(),
              
              // معلومات التكلفة
              _buildDetailRow('التكلفة الإجمالية', '${irrigation.cost.toStringAsFixed(2)} ريال'),
              _buildDetailRow('استهلاك الديزل', '${irrigation.dieselConsumption.toStringAsFixed(2)} لتر'),
              const Divider(),
              
              // معلومات الإدخال
              _buildDetailRow('تاريخ الإدخال', DateFormat('yyyy/MM/dd HH:mm').format(irrigation.createdAt)),
              _buildDetailRow('حالة التاريخ', irrigation.dateStatusText),
              
              // مؤشر التاريخ السابق
              if (irrigation.isBackdated) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                  ),
                  child: const Row(
                    children: [
                      Icon(Icons.info, color: Colors.orange, size: 16),
                      SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          'هذه التسقية تم إدخالها بتاريخ سابق',
                          style: TextStyle(
                            color: Colors.orange,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              
              // الملاحظات
              if (irrigation.notes != null && irrigation.notes!.isNotEmpty) ...[
                const SizedBox(height: 12),
                const Text(
                  'الملاحظات:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(irrigation.notes!),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }
}
