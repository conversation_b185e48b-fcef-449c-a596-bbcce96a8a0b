import 'package:flutter/material.dart';

/// حوار المساعدة لصفحة التقارير المخصصة
class ReportsHelpDialog extends StatelessWidget {
  const ReportsHelpDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Row(
        children: [
          Icon(Icons.help_outline, color: Colors.blue),
          SizedBox(width: 8),
          Text('دليل استخدام التقارير المخصصة'),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSection(
                '📊 أنواع التقارير',
                [
                  '• المفصل: عرض جميع العمليات بالتفصيل',
                  '• الملخص: إحصائيات سريعة وملخصة',
                  '• المقارن: مقارنة بين فترات زمنية',
                ],
              ),
              const SizedBox(height: 16),
              _buildSection(
                '🔍 الفلاتر المتاحة',
                [
                  '• الفترة الزمنية: اختر من تاريخ إلى تاريخ',
                  '• العميل: عرض بيانات عميل محدد',
                  '• المزرعة: عرض بيانات مزرعة محددة',
                  '• نوع العملية: تسقيات أو مدفوعات فقط',
                ],
              ),
              const SizedBox(height: 16),
              _buildSection(
                '📤 خيارات التصدير',
                [
                  '• PDF: ملف منسق للطباعة والمشاركة',
                  '• Excel: جدول بيانات قابل للتحرير',
                  '• مشاركة: إرسال التقرير للآخرين',
                  '• طباعة: طباعة مباشرة',
                ],
              ),
              const SizedBox(height: 16),
              _buildSection(
                '💡 نصائح سريعة',
                [
                  '• استخدم "آخر 30 يوم" للحصول على تقرير سريع',
                  '• اضغط "إعادة تعيين الفلاتر" لمسح جميع الفلاتر',
                  '• استخدم العرض بملء الشاشة للبيانات الكبيرة',
                  '• تحقق من مؤشر حالة البيانات قبل إنشاء التقرير',
                ],
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('فهمت'),
        ),
      ],
    );
  }

  Widget _buildSection(String title, List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        const SizedBox(height: 8),
        ...items.map((item) => Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Text(
            item,
            style: const TextStyle(fontSize: 14),
          ),
        )),
      ],
    );
  }
}