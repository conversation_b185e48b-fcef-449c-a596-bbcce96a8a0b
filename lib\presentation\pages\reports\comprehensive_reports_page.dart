import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/farm_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/cashbox_model.dart';
import 'package:untitled/data/models/client_account_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/presentation/blocs/client/client_bloc.dart';
import 'package:untitled/presentation/blocs/client/client_event.dart';
import 'package:untitled/presentation/blocs/client/client_state.dart';
import 'package:untitled/presentation/blocs/farm/farm_bloc.dart';
import 'package:untitled/presentation/blocs/farm/farm_event.dart';
import 'package:untitled/presentation/blocs/farm/farm_state.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_bloc.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_event.dart';
import 'package:untitled/presentation/blocs/irrigation/irrigation_state.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_bloc.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_event.dart';
import 'package:untitled/presentation/blocs/cashbox/cashbox_state.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_bloc.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_event.dart';
import 'package:untitled/presentation/blocs/client_account/client_account_state.dart';
import 'package:untitled/presentation/blocs/payment/payment_bloc.dart';
import 'package:untitled/presentation/blocs/payment/payment_event.dart';
import 'package:untitled/presentation/blocs/payment/payment_state.dart';
import 'package:untitled/core/theme/app_theme.dart';
import 'package:untitled/services/account_statement_service.dart';
import 'package:untitled/services/pdf_service.dart';
import 'package:printing/printing.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pdf/pdf.dart';

/// صفحة التقارير الشاملة المحسنة
class ComprehensiveReportsPage extends StatefulWidget {
  const ComprehensiveReportsPage({super.key});

  @override
  State<ComprehensiveReportsPage> createState() =>
      _ComprehensiveReportsPageState();
}

class _ComprehensiveReportsPageState extends State<ComprehensiveReportsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // البيانات
  List<ClientModel> _clients = [];
  List<FarmModel> _farms = [];
  List<IrrigationModel> _irrigations = [];
  List<CashboxModel> _cashboxes = [];
  List<ClientAccountModel> _accounts = [];
  List<PaymentModel> _payments = [];

  bool _isLoading = false;
  int _loadedCount = 0;
  final int _totalDataSources = 6;

  // فلاتر التاريخ
  DateTime? _startDate;
  DateTime? _endDate;
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');

  @override
  void initState() {
    super.initState();
    debugPrint('🔍 [ComprehensiveReportsPage] initState started');
    _tabController = TabController(length: 5, vsync: this);
    _initializeDateRange();
    _loadAllData();
    debugPrint('🔍 [ComprehensiveReportsPage] initState completed');
  }

  void _initializeDateRange() {
    debugPrint('🔍 [ComprehensiveReportsPage] _initializeDateRange started');
    final now = DateTime.now();
    _startDate = DateTime(now.year, now.month, 1); // بداية الشهر الحالي
    _endDate = now; // اليوم الحالي
    debugPrint('🔍 [ComprehensiveReportsPage] _initializeDateRange completed');
  }

  @override
  void dispose() {
    debugPrint('🔍 [ComprehensiveReportsPage] dispose started');
    _tabController.dispose();
    super.dispose();
    debugPrint('🔍 [ComprehensiveReportsPage] dispose completed');
  }

  void _loadAllData() {
    debugPrint('🔍 [ComprehensiveReportsPage] _loadAllData started');
    setState(() {
      _isLoading = true;
      _loadedCount = 0;
    });

    context.read<ClientBloc>().add(const LoadClients());
    context.read<FarmBloc>().add(const LoadFarms());
    context.read<IrrigationBloc>().add(const LoadIrrigations());
    context.read<CashboxBloc>().add(const LoadCashboxes());
    context.read<ClientAccountBloc>().add(const LoadAllClientAccounts());
    context.read<PaymentBloc>().add(const LoadPayments());
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _loadAllData completed - events dispatched');
  }

  void _checkDataLoaded() {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _checkDataLoaded - count: $_loadedCount');
    _loadedCount++;
    if (_loadedCount >= _totalDataSources) {
      debugPrint(
          '🔍 [ComprehensiveReportsPage] All data loaded, setting isLoading to false');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] build started - isLoading: $_isLoading');
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: _buildAppBar(),
      body: MultiBlocListener(
        listeners: _buildBlocListeners(),
        child: _isLoading
            ? _buildLoadingWidget()
            : Column(
                children: [
                  _buildDateFilterSection(),
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        _buildOverviewTab(),
                        _buildClientsTab(),
                        _buildFarmsTab(),
                        _buildIrrigationsTab(),
                        _buildFinancialTab(),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] build completed');
  }

  PreferredSizeWidget _buildAppBar() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildAppBar started');
    return AppBar(
      title: const Text(
        'التقارير الشاملة',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      backgroundColor: AppTheme.primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _loadAllData,
          tooltip: 'تحديث البيانات',
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.picture_as_pdf),
          tooltip: 'تصدير PDF',
          onSelected: (value) {
            switch (value) {
              case 'summary':
                _exportSummaryPdf(context);
                break;
              case 'detailed':
                _exportDetailedPdf(context);
                break;
              case 'comparison':
                _exportComparisonPdf(context);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'summary',
              child: Row(
                children: [
                  Icon(Icons.summarize, color: Colors.green),
                  SizedBox(width: 8),
                  Text('تقرير ملخص'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'detailed',
              child: Row(
                children: [
                  Icon(Icons.table_chart, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('تقرير مفصل'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'comparison',
              child: Row(
                children: [
                  Icon(Icons.compare_arrows, color: Colors.orange),
                  SizedBox(width: 8),
                  Text('تقرير مقارن'),
                ],
              ),
            ),
          ],
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: Colors.white,
        indicatorWeight: 3,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        labelStyle: const TextStyle(fontWeight: FontWeight.bold),
        tabs: const [
          Tab(icon: Icon(Icons.dashboard), text: 'نظرة عامة'),
          Tab(icon: Icon(Icons.people), text: 'العملاء'),
          Tab(icon: Icon(Icons.landscape), text: 'المزارع'),
          Tab(icon: Icon(Icons.water_drop), text: 'التسقيات'),
          Tab(icon: Icon(Icons.account_balance), text: 'المالية'),
        ],
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildAppBar completed');
  }

  List<BlocListener> _buildBlocListeners() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildBlocListeners started');
    return [
      BlocListener<ClientBloc, ClientState>(
        listener: (context, state) {
          debugPrint(
              '🔍 [ComprehensiveReportsPage] ClientBloc state: ${state.runtimeType}');
          if (state is ClientsLoaded) {
            debugPrint(
                '🔍 [ComprehensiveReportsPage] Clients loaded: ${state.clients.length}');
            setState(() => _clients = state.clients);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<FarmBloc, FarmState>(
        listener: (context, state) {
          debugPrint(
              '🔍 [ComprehensiveReportsPage] FarmBloc state: ${state.runtimeType}');
          if (state is FarmsLoaded) {
            debugPrint(
                '🔍 [ComprehensiveReportsPage] Farms loaded: ${state.farms.length}');
            setState(() => _farms = state.farms);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<IrrigationBloc, IrrigationState>(
        listener: (context, state) {
          debugPrint(
              '🔍 [ComprehensiveReportsPage] IrrigationBloc state: ${state.runtimeType}');
          if (state is IrrigationsLoaded) {
            debugPrint(
                '🔍 [ComprehensiveReportsPage] Irrigations loaded: ${state.irrigations.length}');
            setState(() => _irrigations = state.irrigations);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<CashboxBloc, CashboxState>(
        listener: (context, state) {
          debugPrint(
              '🔍 [ComprehensiveReportsPage] CashboxBloc state: ${state.runtimeType}');
          if (state is CashboxesLoaded) {
            debugPrint(
                '🔍 [ComprehensiveReportsPage] Cashboxes loaded: ${state.cashboxes.length}');
            setState(() => _cashboxes = state.cashboxes);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<ClientAccountBloc, ClientAccountState>(
        listener: (context, state) {
          debugPrint(
              '🔍 [ComprehensiveReportsPage] ClientAccountBloc state: ${state.runtimeType}');
          if (state is AllClientAccountsLoaded) {
            debugPrint(
                '🔍 [ComprehensiveReportsPage] ClientAccounts loaded: ${state.accounts.length}');
            setState(() => _accounts = state.accounts);
            _checkDataLoaded();
          }
        },
      ),
      BlocListener<PaymentBloc, PaymentState>(
        listener: (context, state) {
          debugPrint(
              '🔍 [ComprehensiveReportsPage] PaymentBloc state: ${state.runtimeType}');
          if (state is PaymentsLoaded) {
            debugPrint(
                '🔍 [ComprehensiveReportsPage] Payments loaded: ${state.payments.length}');
            setState(() => _payments = state.payments);
            _checkDataLoaded();
          }
        },
      ),
    ];
    debugPrint('🔍 [ComprehensiveReportsPage] _buildBlocListeners completed');
  }

  Widget _buildLoadingWidget() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildLoadingWidget started');
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري تحميل البيانات...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'تم تحميل $_loadedCount من $_totalDataSources',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildLoadingWidget completed');
  }

  Widget _buildDateFilterSection() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildDateFilterSection started');
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(Icons.date_range, color: AppTheme.primaryColor),
          const SizedBox(width: 8),
          const Text(
            'فترة التقرير:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: _buildDateButton(
                    'من: ${_dateFormat.format(_startDate!)}',
                    () => _selectStartDate(),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildDateButton(
                    'إلى: ${_dateFormat.format(_endDate!)}',
                    () => _selectEndDate(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildDateFilterSection completed');
  }

  Widget _buildDateButton(String text, VoidCallback onPressed) {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildDateButton - text: $text');
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        side: const BorderSide(color: AppTheme.primaryColor),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      child: Text(
        text,
        style: const TextStyle(color: AppTheme.primaryColor),
      ),
    );
  }

  Future<void> _selectStartDate() async {
    debugPrint('🔍 [ComprehensiveReportsPage] _selectStartDate started');
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate!,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() => _startDate = date);
      debugPrint(
          '🔍 [ComprehensiveReportsPage] _selectStartDate - selected: $date');
    }
    debugPrint('🔍 [ComprehensiveReportsPage] _selectStartDate completed');
  }

  Future<void> _selectEndDate() async {
    debugPrint('🔍 [ComprehensiveReportsPage] _selectEndDate started');
    final date = await showDatePicker(
      context: context,
      initialDate: _endDate!,
      firstDate: _startDate!,
      lastDate: DateTime.now(),
    );
    if (date != null) {
      setState(() => _endDate = date);
      debugPrint(
          '🔍 [ComprehensiveReportsPage] _selectEndDate - selected: $date');
    }
    debugPrint('🔍 [ComprehensiveReportsPage] _selectEndDate completed');
  }

  // تبويب النظرة العامة
  Widget _buildOverviewTab() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildOverviewTab started');
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('الإحصائيات العامة', Icons.analytics),
          const SizedBox(height: 16),
          _buildOverviewStats(),
          const SizedBox(height: 24),
          _buildSectionHeader('الأنشطة الحديثة', Icons.history),
          const SizedBox(height: 16),
          _buildRecentActivities(),
          const SizedBox(height: 24),
          _buildSectionHeader('الرسوم البيانية', Icons.bar_chart),
          const SizedBox(height: 16),
          _buildChartsSection(),
        ],
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildOverviewTab completed');
  }

  Widget _buildOverviewStats() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildOverviewStats started');
    final totalRevenue = _calculateTotalRevenue();
    final totalExpenses = _calculateTotalExpenses();
    final netProfit = totalRevenue - totalExpenses;
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildOverviewStats - revenue: $totalRevenue, expenses: $totalExpenses, profit: $netProfit');

    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.3,
      children: [
        _buildStatCard(
          'إجمالي العملاء',
          '${_clients.length}',
          Icons.people,
          Colors.blue,
        ),
        _buildStatCard(
          'إجمالي المزارع',
          '${_farms.length}',
          Icons.landscape,
          Colors.green,
        ),
        _buildStatCard(
          'إجمالي التسقيات',
          '${_irrigations.length}',
          Icons.water_drop,
          Colors.cyan,
        ),
        _buildStatCard(
          'إجمالي الإيرادات',
          '${totalRevenue.toStringAsFixed(0)} ريال',
          Icons.trending_up,
          Colors.green,
        ),
        _buildStatCard(
          'إجمالي المصروفات',
          '${totalExpenses.toStringAsFixed(0)} ريال',
          Icons.trending_down,
          Colors.red,
        ),
        _buildStatCard(
          'صافي الربح',
          '${netProfit.toStringAsFixed(0)} ريال',
          Icons.account_balance_wallet,
          netProfit >= 0 ? Colors.green : Colors.red,
        ),
      ],
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildOverviewStats completed');
  }

  Widget _buildRecentActivities() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildRecentActivities started');
    final recentIrrigations = _irrigations
        .where((i) => i.createdAt
            .isAfter(DateTime.now().subtract(const Duration(days: 7))))
        .take(5)
        .toList();
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildRecentActivities - found ${recentIrrigations.length} recent irrigations');

    if (recentIrrigations.isEmpty) {
      debugPrint(
          '🔍 [ComprehensiveReportsPage] _buildRecentActivities - no recent activities');
      return _buildEmptyState('لا توجد أنشطة حديثة', Icons.history);
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: recentIrrigations.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final irrigation = recentIrrigations[index];
          final client = _clients.firstWhere(
            (c) => c.id == irrigation.clientId,
            orElse: () => ClientModel(
              name: 'عميل غير معروف',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          );

          return ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.blue.withValues(alpha: 0.1),
              child: const Icon(Icons.water_drop, color: Colors.blue),
            ),
            title: Text(
              'تسقية لعميل ${client.name}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              'التكلفة: ${irrigation.cost.toStringAsFixed(0)} ريال • '
              'المدة: ${(irrigation.duration / 60).toStringAsFixed(1)} ساعة',
            ),
            trailing: Text(
              _dateFormat.format(irrigation.createdAt),
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
            ),
          );
        },
      ),
    );
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildRecentActivities completed');
  }

  Widget _buildChartsSection() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildChartsSection started');
    return Column(
      children: [
        _buildMonthlyRevenueChart(),
        const SizedBox(height: 16),
        _buildClientDistributionChart(),
      ],
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildChartsSection completed');
  }

  Widget _buildMonthlyRevenueChart() {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildMonthlyRevenueChart started');
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: const Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الإيرادات الشهرية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            SizedBox(
              height: 200,
              width: double.infinity,
              child: Center(
                child: Text(
                  'الرسم البياني للإيرادات الشهرية\n(سيتم تطويره لاحقاً)',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ),
          ],
        ),
      ),
    );
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildMonthlyRevenueChart completed');
  }

  Widget _buildClientDistributionChart() {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildClientDistributionChart started');
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: const Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'توزيع العملاء حسب النشاط',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            SizedBox(
              height: 200,
              width: double.infinity,
              child: Center(
                child: Text(
                  'الرسم البياني لتوزيع العملاء\n(سيتم تطويره لاحقاً)',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ),
          ],
        ),
      ),
    );
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildClientDistributionChart completed');
  }

  // تبويب العملاء
  Widget _buildClientsTab() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildClientsTab started');
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('إحصائيات العملاء', Icons.people),
          const SizedBox(height: 16),
          _buildClientsStats(),
          const SizedBox(height: 24),
          _buildSectionHeader('قائمة العملاء', Icons.list),
          const SizedBox(height: 16),
          _buildClientsList(),
        ],
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildClientsTab completed');
  }

  Widget _buildClientsStats() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildClientsStats started');
    final activeClients = _clients
        .where((c) => _irrigations.any((i) => i.clientId == c.id))
        .length;

    final clientsWithAccounts = _accounts.length;
    final averageFarmsPerClient = _clients.isNotEmpty
        ? (_farms.length / _clients.length).toStringAsFixed(1)
        : '0';
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildClientsStats - activeClients: $activeClients, clientsWithAccounts: $clientsWithAccounts, averageFarmsPerClient: $averageFarmsPerClient');

    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.3,
      children: [
        _buildStatCard(
          'إجمالي العملاء',
          '${_clients.length}',
          Icons.people,
          Colors.blue,
        ),
        _buildStatCard(
          'العملاء النشطون',
          '$activeClients',
          Icons.person_add,
          Colors.green,
        ),
        _buildStatCard(
          'العملاء ذوو الحسابات',
          '$clientsWithAccounts',
          Icons.account_balance,
          Colors.orange,
        ),
        _buildStatCard(
          'متوسط المزارع للعميل',
          averageFarmsPerClient,
          Icons.landscape,
          Colors.purple,
        ),
      ],
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildClientsStats completed');
  }

  Widget _buildClientsList() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildClientsList started');
    if (_clients.isEmpty) {
      debugPrint(
          '🔍 [ComprehensiveReportsPage] _buildClientsList - no clients');
      return _buildEmptyState('لا يوجد عملاء', Icons.people);
    }
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildClientsList - building list for ${_clients.length} clients');

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: _clients.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final client = _clients[index];
          final clientFarms =
              _farms.where((f) => f.clientId == client.id).length;
          final clientIrrigations =
              _irrigations.where((i) => i.clientId == client.id).length;
          final hasAccount = _accounts.any((a) => a.clientId == client.id);

          return ListTile(
            leading: CircleAvatar(
              backgroundColor: hasAccount
                  ? Colors.green.withOpacity(0.1)
                  : Colors.grey.withOpacity(0.1),
              child: Icon(
                hasAccount ? Icons.person : Icons.person_outline,
                color: hasAccount ? Colors.green : Colors.grey,
              ),
            ),
            title: Text(
              client.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (client.phone != null) Text('الهاتف: ${client.phone}'),
                Text('المزارع: $clientFarms • التسقيات: $clientIrrigations'),
              ],
            ),
            trailing: SizedBox(
              width: 70,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (hasAccount)
                    const Icon(Icons.verified, color: Colors.green, size: 16),
                  Text(
                    _dateFormat.format(client.createdAt),
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            onTap: () => _showClientDetails(client),
          );
        },
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildClientsList completed');
  }

  // تبويب المزارع
  Widget _buildFarmsTab() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFarmsTab started');
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('إحصائيات المزارع', Icons.landscape),
          const SizedBox(height: 16),
          _buildFarmsStats(),
          const SizedBox(height: 24),
          _buildSectionHeader('قائمة المزارع', Icons.list),
          const SizedBox(height: 16),
          _buildFarmsList(),
        ],
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFarmsTab completed');
  }

  Widget _buildFarmsStats() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFarmsStats started');
    final activeFarms =
        _farms.where((f) => _irrigations.any((i) => i.farmId == f.id)).length;

    final averageIrrigationsPerFarm = _farms.isNotEmpty
        ? (_irrigations.length / _farms.length).toStringAsFixed(1)
        : '0';
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildFarmsStats - activeFarms: $activeFarms, averageIrrigationsPerFarm: $averageIrrigationsPerFarm');

    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.3,
      children: [
        _buildStatCard(
          'إجمالي المزارع',
          '${_farms.length}',
          Icons.landscape,
          Colors.green,
        ),
        _buildStatCard(
          'المزارع النشطة',
          '$activeFarms',
          Icons.eco,
          Colors.lightGreen,
        ),
        _buildStatCard(
          'المزارع غير النشطة',
          '${_farms.length - activeFarms}',
          Icons.nature,
          Colors.orange,
        ),
        _buildStatCard(
          'متوسط التسقيات/مزرعة',
          averageIrrigationsPerFarm,
          Icons.analytics,
          Colors.blue,
        ),
      ],
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFarmsStats completed');
  }

  Widget _buildFarmsList() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFarmsList started');
    if (_farms.isEmpty) {
      debugPrint('🔍 [ComprehensiveReportsPage] _buildFarmsList - no farms');
      return _buildEmptyState('لا توجد مزارع', Icons.landscape);
    }
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildFarmsList - building list for ${_farms.length} farms');

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: _farms.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final farm = _farms[index];
          final client = _clients.firstWhere(
            (c) => c.id == farm.clientId,
            orElse: () => ClientModel(
              name: 'عميل غير معروف',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          );
          final farmIrrigations =
              _irrigations.where((i) => i.farmId == farm.id).length;
          final isActive = farmIrrigations > 0;

          return ListTile(
            leading: CircleAvatar(
              backgroundColor: isActive
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.grey.withValues(alpha: 0.1),
              child: Icon(
                isActive ? Icons.eco : Icons.landscape,
                color: isActive ? Colors.green : Colors.grey,
              ),
            ),
            title: Text(
              farm.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('العميل: ${client.name}'),
                if (farm.location != null) Text('الموقع: ${farm.location}'),
                Text('التسقيات: $farmIrrigations'),
              ],
            ),
            trailing: SizedBox(
              width: 70,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (isActive)
                    const Icon(Icons.check_circle,
                        color: Colors.green, size: 16),
                  Text(
                    _dateFormat.format(farm.createdAt),
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            onTap: () => _showFarmDetails(farm),
          );
        },
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFarmsList completed');
  }

  // تبويب التسقيات
  Widget _buildIrrigationsTab() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildIrrigationsTab started');
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('قائمة التسقيات', Icons.list),
          const SizedBox(height: 16),
          _buildIrrigationsStats(),
          const SizedBox(height: 24),
          _buildIrrigationsList(),
        ],
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildIrrigationsTab completed');
  }

  Widget _buildIrrigationsStats() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildIrrigationsStats started');
    double totalCost = 0.0;
    double totalDiesel = 0.0;
    int totalMinutes = 0;

    for (final irrigation in _irrigations) {
      totalCost += irrigation.cost;
      totalDiesel += irrigation.dieselConsumption;
      totalMinutes += irrigation.duration;
    }

    final averageCost = _irrigations.isNotEmpty
        ? (totalCost / _irrigations.length).toStringAsFixed(0)
        : '0';
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildIrrigationsStats - totalCost: $totalCost, totalDiesel: $totalDiesel, totalMinutes: $totalMinutes, averageCost: $averageCost');

    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.3,
      children: [
        _buildStatCard(
          'إجمالي التسقيات',
          '${_irrigations.length}',
          Icons.water_drop,
          Colors.blue,
        ),
        _buildStatCard(
          'إجمالي التكلفة',
          '${totalCost.toStringAsFixed(0)} ريال',
          Icons.attach_money,
          Colors.green,
        ),
        _buildStatCard(
          'إجمالي الديزل',
          '${totalDiesel.toStringAsFixed(1)} لتر',
          Icons.local_gas_station,
          Colors.orange,
        ),
        _buildStatCard(
          'إجمالي الوقت',
          '${(totalMinutes / 60).toStringAsFixed(1)} ساعة',
          Icons.timer,
          Colors.purple,
        ),
        _buildStatCard(
          'متوسط التكلفة',
          '$averageCost ريال',
          Icons.analytics,
          Colors.cyan,
        ),
        _buildStatCard(
          'متوسط المدة',
          '${_irrigations.isNotEmpty ? (totalMinutes / _irrigations.length / 60).toStringAsFixed(1) : '0'} ساعة',
          Icons.schedule,
          Colors.indigo,
        ),
      ],
    );
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildIrrigationsStats completed');
  }

  Widget _buildIrrigationsList() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildIrrigationsList started');
    if (_irrigations.isEmpty) {
      debugPrint(
          '🔍 [ComprehensiveReportsPage] _buildIrrigationsList - no irrigations');
      return _buildEmptyState('لا توجد تسقيات', Icons.water_drop);
    }
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildIrrigationsList - building list for ${_irrigations.length} irrigations');

    // ترتيب التسقيات حسب التاريخ (الأحدث أولاً)
    final sortedIrrigations = List<IrrigationModel>.from(_irrigations)
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: sortedIrrigations.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final irrigation = sortedIrrigations[index];
          final client = _clients.firstWhere(
            (c) => c.id == irrigation.clientId,
            orElse: () => ClientModel(
              name: 'عميل غير معروف',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          );
          // البحث عن المزرعة باستخدام مقارنة toString لضمان التطابق
          final farm = _farms.firstWhere(
            (f) =>
                f.id != null &&
                f.id!.toString() == irrigation.farmId.toString(),
            orElse: () => FarmModel(
              clientId: irrigation.clientId,
              name: 'مزرعة غير معروفة',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          );

          return ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.blue.withValues(alpha: 0.1),
              child: const Icon(Icons.water_drop, color: Colors.blue),
            ),
            title: Text(
              '${client.name} - ${farm.name}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('التكلفة: ${irrigation.cost.toStringAsFixed(0)} ريال'),
                Text(
                    'المدة: ${(irrigation.duration / 60).toStringAsFixed(1)} ساعة'),
                Text(
                    'الديزل: ${irrigation.dieselConsumption.toStringAsFixed(1)} لتر'),
              ],
            ),
            trailing: SizedBox(
              width: 70,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    _dateFormat.format(irrigation.startTime),
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    DateFormat('HH:mm').format(irrigation.startTime),
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
            onTap: () => _showIrrigationDetails(irrigation),
          );
        },
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildIrrigationsList completed');
  }

  // تبويب المالية
  Widget _buildFinancialTab() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFinancialTab started');
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader('الإحصائيات المالية', Icons.account_balance),
          const SizedBox(height: 16),
          _buildFinancialStats(),
          const SizedBox(height: 24),
          _buildSectionHeader('أرصدة الصناديق', Icons.account_balance_wallet),
          const SizedBox(height: 16),
          _buildCashboxesList(),
          const SizedBox(height: 24),
          _buildSectionHeader('أرصدة العملاء', Icons.people),
          const SizedBox(height: 16),
          _buildAccountsList(),
        ],
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFinancialTab completed');
  }

  Widget _buildFinancialStats() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFinancialStats started');
    double totalCashBalance = 0.0;
    double totalDieselBalance = 0.0;

    for (final cashbox in _cashboxes) {
      if (cashbox.type == 'cash') {
        totalCashBalance += cashbox.balance;
      } else {
        totalDieselBalance += cashbox.balance;
      }
    }

    double totalClientCash = 0.0;
    double totalClientDiesel = 0.0;
    for (final account in _accounts) {
      totalClientCash += account.cashBalance;
      totalClientDiesel += account.dieselBalance;
    }

    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.3,
      children: [
        _buildStatCard(
          'إجمالي النقد',
          '${totalCashBalance.toStringAsFixed(0)} ريال',
          Icons.attach_money,
          Colors.green,
        ),
        _buildStatCard(
          'إجمالي الديزل',
          '${totalDieselBalance.toStringAsFixed(1)} لتر',
          Icons.local_gas_station,
          Colors.orange,
        ),
        _buildStatCard(
          'أرصدة العملاء النقدية',
          '${totalClientCash.toStringAsFixed(0)} ريال',
          Icons.account_circle,
          totalClientCash >= 0 ? Colors.green : Colors.red,
        ),
        _buildStatCard(
          'أرصدة العملاء الديزل',
          '${totalClientDiesel.toStringAsFixed(1)} لتر',
          Icons.local_gas_station,
          totalClientDiesel >= 0 ? Colors.green : Colors.red,
        ),
        _buildStatCard(
          'عدد الصناديق',
          '${_cashboxes.length}',
          Icons.account_balance_wallet,
          Colors.blue,
        ),
        _buildStatCard(
          'عدد الحسابات',
          '${_accounts.length}',
          Icons.people,
          Colors.purple,
        ),
      ],
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildFinancialStats completed');
  }

  Widget _buildCashboxesList() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildCashboxesList started');
    if (_cashboxes.isEmpty) {
      debugPrint(
          '🔍 [ComprehensiveReportsPage] _buildCashboxesList - no cashboxes');
      return _buildEmptyState('لا توجد صناديق', Icons.account_balance_wallet);
    }
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildCashboxesList - building list for ${_cashboxes.length} cashboxes');

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: _cashboxes.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final cashbox = _cashboxes[index];
          final isCash = cashbox.type == 'cash';
          final isPositive = cashbox.balance >= 0;

          return ListTile(
            leading: CircleAvatar(
              backgroundColor: isCash
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.orange.withValues(alpha: 0.1),
              child: Icon(
                isCash ? Icons.attach_money : Icons.local_gas_station,
                color: isCash ? Colors.green : Colors.orange,
              ),
            ),
            title: Text(
              cashbox.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(isCash ? 'صندوق نقدي' : 'صندوق ديزل'),
            trailing: SizedBox(
              width: 90,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '${cashbox.balance.toStringAsFixed(isCash ? 0 : 1)} ${isCash ? 'ريال' : 'لتر'}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: isPositive ? Colors.green : Colors.red,
                      fontSize: 16,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (!isPositive)
                    const Icon(Icons.warning, color: Colors.red, size: 16),
                ],
              ),
            ),
            onTap: () => _showCashboxDetails(cashbox),
          );
        },
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildCashboxesList completed');
  }

  Widget _buildAccountsList() {
    debugPrint('🔍 [ComprehensiveReportsPage] _buildAccountsList started');
    if (_accounts.isEmpty) {
      debugPrint(
          '🔍 [ComprehensiveReportsPage] _buildAccountsList - no accounts');
      return _buildEmptyState('لا توجد حسابات عملاء', Icons.people);
    }
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildAccountsList - building list for ${_accounts.length} accounts');

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: _accounts.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final account = _accounts[index];
          final client = _clients.firstWhere(
            (c) => c.id == account.clientId,
            orElse: () => ClientModel(
              name: 'عميل غير معروف',
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          );

          final hasCashDebt = account.cashBalance < 0;
          final hasDieselDebt = account.dieselBalance < 0;
          final hasDebt = hasCashDebt || hasDieselDebt;

          return ListTile(
            leading: CircleAvatar(
              backgroundColor: hasDebt
                  ? Colors.red.withValues(alpha: 0.1)
                  : Colors.green.withValues(alpha: 0.1),
              child: Icon(
                hasDebt ? Icons.warning : Icons.account_circle,
                color: hasDebt ? Colors.red : Colors.green,
              ),
            ),
            title: Text(
              client.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'نقدي: ${account.cashBalance.toStringAsFixed(0)} ريال',
                  style: TextStyle(
                    color: account.cashBalance >= 0 ? Colors.green : Colors.red,
                  ),
                ),
                Text(
                  'ديزل: ${account.dieselBalance.toStringAsFixed(1)} لتر',
                  style: TextStyle(
                    color:
                        account.dieselBalance >= 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
            trailing: hasDebt
                ? const Icon(Icons.priority_high, color: Colors.red)
                : const Icon(Icons.check_circle, color: Colors.green),
            onTap: () => _showAccountDetails(account),
          );
        },
      ),
    );
    debugPrint('🔍 [ComprehensiveReportsPage] _buildAccountsList completed');
  }

  // الأدوات المساعدة
  Widget _buildSectionHeader(String title, IconData icon) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildSectionHeader - title: $title');
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: AppTheme.primaryColor, size: 20),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _buildEmptyState - message: $message');
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // الحسابات والعمليات
  double _calculateTotalRevenue() {
    debugPrint('🔍 [ComprehensiveReportsPage] _calculateTotalRevenue started');
    final irrigationRevenue =
        _irrigations.fold(0.0, (sum, irrigation) => sum + irrigation.cost);
    final paymentRevenue = _payments
        .where((p) => p.amount > 0)
        .fold(0.0, (sum, payment) => sum + payment.amount);
    final total = irrigationRevenue + paymentRevenue;
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _calculateTotalRevenue - irrigation: $irrigationRevenue, payment: $paymentRevenue, total: $total');
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _calculateTotalRevenue completed');
    return total;
  }

  double _calculateTotalExpenses() {
    debugPrint('🔍 [ComprehensiveReportsPage] _calculateTotalExpenses started');
    final irrigationExpenses = _irrigations.fold(
        0.0, (sum, irrigation) => sum + (irrigation.dieselConsumption * 2.5));
    final paymentExpenses = _payments
        .where((p) => p.amount < 0)
        .fold(0.0, (sum, payment) => sum + payment.amount.abs());
    final total = irrigationExpenses + paymentExpenses;
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _calculateTotalExpenses - irrigation: $irrigationExpenses, payment: $paymentExpenses, total: $total');
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _calculateTotalExpenses completed');
    return total;
  }

  // عرض التفاصيل
  void _showClientDetails(ClientModel client) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _showClientDetails - client: ${client.name}');
    Navigator.pushNamed(context, '/client-details', arguments: client.id);
  }

  void _showFarmDetails(FarmModel farm) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _showFarmDetails - farm: ${farm.name}');
    Navigator.pushNamed(context, '/farm-details', arguments: farm.id);
  }

  void _showIrrigationDetails(IrrigationModel irrigation) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _showIrrigationDetails - irrigation: ${irrigation.id}');
    Navigator.pushNamed(context, '/irrigation-details',
        arguments: irrigation.id);
  }

  void _showCashboxDetails(CashboxModel cashbox) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _showCashboxDetails - cashbox: ${cashbox.name}');
    Navigator.pushNamed(context, '/cashbox-details', arguments: cashbox.id);
  }

  void _showAccountDetails(ClientAccountModel account) {
    debugPrint(
        '🔍 [ComprehensiveReportsPage] _showAccountDetails - account: ${account.clientId}');
    Navigator.pushNamed(context, '/client-account-details',
        arguments: account.clientId);
  }

  // أضف دالة توليد وطباعة كشف الحساب PDF
  Future<void> _printClientStatement(ClientModel client) async {
    try {
      // جلب كشف الحساب من الخدمة
      final statement = await AccountStatementService().generateClientStatement(
        clientId: client.id.toString(),
        fromDate: _startDate!,
        toDate: _endDate!,
      );

      // توليد PDF احترافي بالعربية
      final pdfFile = await PdfService().createClientStatementPdf(
        statement: statement,
        logoAssetPath: 'assets/images/app_logo.png', // عدل المسار إذا لزم
      );

      // مشاركة أو عرض PDF مباشرة
      await Printing.sharePdf(
        bytes: await pdfFile.readAsBytes(),
        filename: 'كشف حساب ${client.name}.pdf',
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء توليد كشف الحساب: $e')),
      );
    }
  }

  // دوال فلترة البيانات حسب التاريخ
  List<IrrigationModel> _getFilteredIrrigations() {
    if (_startDate == null || _endDate == null) return _irrigations;
    
    return _irrigations.where((irrigation) {
      final irrigationDate = irrigation.startTime;
      return irrigationDate.isAfter(_startDate!.subtract(const Duration(days: 1))) &&
             irrigationDate.isBefore(_endDate!.add(const Duration(days: 1)));
    }).toList();
  }

  List<PaymentModel> _getFilteredPayments() {
    if (_startDate == null || _endDate == null) return _payments;
    
    return _payments.where((payment) {
      final paymentDate = payment.paymentDate;
      return paymentDate.isAfter(_startDate!.subtract(const Duration(days: 1))) &&
             paymentDate.isBefore(_endDate!.add(const Duration(days: 1)));
    }).toList();
  }

  List<CashboxModel> _getFilteredCashboxes() {
    if (_startDate == null || _endDate == null) return _cashboxes;
    
    return _cashboxes.where((cashbox) {
      final cashboxDate = cashbox.createdAt;
      return cashboxDate.isAfter(_startDate!.subtract(const Duration(days: 1))) &&
             cashboxDate.isBefore(_endDate!.add(const Duration(days: 1)));
    }).toList();
  }

  // حساب الإحصائيات بناءً على البيانات المفلترة
  double _calculateFilteredTotalRevenue() {
    final filteredIrrigations = _getFilteredIrrigations();
    return filteredIrrigations.fold<double>(0, (sum, irrigation) => sum + irrigation.cost);
  }

  double _calculateFilteredTotalExpenses() {
    final filteredPayments = _getFilteredPayments();
    return filteredPayments.fold<double>(0, (sum, payment) => sum + payment.amount);
  }

  // دالة إنشاء PDF للتقرير الشامل (مفصل)
  Future<pw.Document> _createPdfDocument() async {
    if (_clients.isEmpty && _farms.isEmpty && _irrigations.isEmpty) {
      throw Exception('لا توجد بيانات لتصديرها');
    }

      final pdf = pw.Document();
      // استخدام الخط الافتراضي بدلاً من تحميل ملف خارجي
      final font = pw.Font.helvetica();
      final boldFont = pw.Font.helveticaBold();

      // البيانات المفلترة
      final filteredIrrigations = _getFilteredIrrigations();
      final filteredPayments = _getFilteredPayments();
      final filteredCashboxes = _getFilteredCashboxes();
      
      // حساب الإحصائيات من البيانات المفلترة
      final totalRevenue = _calculateFilteredTotalRevenue();
      final totalExpenses = _calculateFilteredTotalExpenses();
      final netProfit = totalRevenue - totalExpenses;
      final totalIrrigationHours = filteredIrrigations.fold<double>(0, (sum, i) => sum + i.duration);
      final totalDieselConsumption = filteredIrrigations.fold<double>(0, (sum, i) => sum + i.dieselConsumption);

      pdf.addPage(
        pw.MultiPage(
          theme: pw.ThemeData.withFont(
            base: font,
            bold: boldFont,
          ),
          textDirection: pw.TextDirection.rtl,
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(20),
          build: (context) => [
            // رأس التقرير
            pw.Header(
              level: 0,
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text('التقرير الشامل - مفصل',
                          style: pw.TextStyle(
                              font: boldFont,
                              fontSize: 24,
                              fontWeight: pw.FontWeight.bold)),
                      if (_startDate != null && _endDate != null)
                        pw.Text('الفترة: ${DateFormat('yyyy-MM-dd').format(_startDate!)} - ${DateFormat('yyyy-MM-dd').format(_endDate!)}',
                            style: pw.TextStyle(font: font, fontSize: 12)),
                    ],
                  ),
                  pw.Text(
                      'تاريخ الطباعة: ${DateTime.now().toString().split(' ')[0]}',
                      style: pw.TextStyle(font: font, fontSize: 12)),
                ],
              ),
            ),
            pw.SizedBox(height: 16),

            // الملخص التنفيذي
            pw.Container(
              padding: const pw.EdgeInsets.all(16),
              decoration: pw.BoxDecoration(
                color: PdfColor.fromInt(0xFFF8F9FA),
                borderRadius: pw.BorderRadius.circular(12),
                border: pw.Border.all(color: PdfColor.fromInt(0xFFDEE2E6)),
              ),
              child: pw.Column(
                children: [
                  pw.Text('الملخص التنفيذي',
                      style: pw.TextStyle(font: boldFont, fontSize: 18)),
                  pw.SizedBox(height: 16),
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                    children: [
                      pw.Column(
                        children: [
                          pw.Text('عدد العملاء',
                              style: pw.TextStyle(font: font, fontSize: 12)),
                          pw.Text('${_clients.length}',
                              style: pw.TextStyle(font: boldFont, fontSize: 16, color: PdfColors.blue700)),
                        ],
                      ),
                      pw.Column(
                        children: [
                          pw.Text('عدد المزارع',
                              style: pw.TextStyle(font: font, fontSize: 12)),
                          pw.Text('${_farms.length}',
                              style: pw.TextStyle(font: boldFont, fontSize: 16, color: PdfColors.green700)),
                        ],
                      ),
                      pw.Column(
                        children: [
                          pw.Text('عدد التسقيات',
                              style: pw.TextStyle(font: font, fontSize: 12)),
                          pw.Text('${filteredIrrigations.length}',
                              style: pw.TextStyle(font: boldFont, fontSize: 16, color: PdfColors.orange700)),
                        ],
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 16),
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                    children: [
                      pw.Column(
                        children: [
                          pw.Text('إجمالي الإيرادات',
                              style: pw.TextStyle(font: font, fontSize: 12)),
                          pw.Text('${totalRevenue.toStringAsFixed(2)} ريال',
                              style: pw.TextStyle(font: boldFont, fontSize: 14, color: PdfColors.green700)),
                        ],
                      ),
                      pw.Column(
                        children: [
                          pw.Text('إجمالي المصروفات',
                              style: pw.TextStyle(font: font, fontSize: 12)),
                          pw.Text('${totalExpenses.toStringAsFixed(2)} ريال',
                              style: pw.TextStyle(font: boldFont, fontSize: 14, color: PdfColors.red700)),
                        ],
                      ),
                      pw.Column(
                        children: [
                          pw.Text('صافي الربح',
                              style: pw.TextStyle(font: font, fontSize: 12)),
                          pw.Text('${netProfit.toStringAsFixed(2)} ريال',
                              style: pw.TextStyle(
                                font: boldFont, 
                                fontSize: 14, 
                                color: netProfit >= 0 ? PdfColors.green700 : PdfColors.red700
                              )),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            pw.SizedBox(height: 20),

            // جدول العملاء
            if (_clients.isNotEmpty) ...[
              pw.Text('تفاصيل العملاء',
                  style: pw.TextStyle(font: boldFont, fontSize: 16)),
              pw.SizedBox(height: 8),
              pw.TableHelper.fromTextArray(
                cellAlignment: pw.Alignment.center,
                headerStyle: pw.TextStyle(
                    font: boldFont, fontWeight: pw.FontWeight.bold, fontSize: 11),
                cellStyle: pw.TextStyle(font: font, fontSize: 10),
                headerDecoration:
                    const pw.BoxDecoration(color: PdfColor.fromInt(0xFFE3F2FD)),
                headers: [
                  'الرقم',
                  'اسم العميل',
                  'رقم الهاتف',
                  'العنوان',
                  'الملاحظات',
                ],
                data: _clients.map((client) => [
                      client.id.toString(),
                      client.name,
                      client.phone ?? '-',
                      client.address ?? '-',
                      client.notes ?? '-',
                    ]).toList(),
                cellAlignments: {
                  0: pw.Alignment.center,
                  1: pw.Alignment.centerRight,
                  2: pw.Alignment.centerRight,
                  3: pw.Alignment.centerRight,
                  4: pw.Alignment.centerRight,
                },
              ),
              pw.SizedBox(height: 20),
            ],

            // جدول المزارع
            if (_farms.isNotEmpty) ...[
              pw.Text('تفاصيل المزارع',
                  style: pw.TextStyle(font: boldFont, fontSize: 16)),
              pw.SizedBox(height: 8),
              pw.TableHelper.fromTextArray(
                cellAlignment: pw.Alignment.center,
                headerStyle: pw.TextStyle(
                    font: boldFont, fontWeight: pw.FontWeight.bold, fontSize: 11),
                cellStyle: pw.TextStyle(font: font, fontSize: 10),
                headerDecoration:
                    const pw.BoxDecoration(color: PdfColor.fromInt(0xFFE8F5E8)),
                headers: [
                  'الرقم',
                  'اسم المزرعة',
                  'العميل',
                  'الموقع',
                  'الملاحظات',
                ],
                data: _farms.map((farm) => [
                      farm.id.toString(),
                      farm.name,
                      'عميل ${farm.clientId}',
                      farm.location ?? '-',
                      farm.notes ?? '-',
                    ]).toList(),
                cellAlignments: {
                  0: pw.Alignment.center,
                  1: pw.Alignment.centerRight,
                  2: pw.Alignment.centerRight,
                  3: pw.Alignment.centerRight,
                  4: pw.Alignment.centerRight,
                },
              ),
              pw.SizedBox(height: 20),
            ],

            // جدول التسقيات
            if (filteredIrrigations.isNotEmpty) ...[
              pw.Text('تفاصيل التسقيات',
                  style: pw.TextStyle(font: boldFont, fontSize: 16)),
              pw.SizedBox(height: 8),
              pw.TableHelper.fromTextArray(
                cellAlignment: pw.Alignment.center,
                headerStyle: pw.TextStyle(
                    font: boldFont, fontWeight: pw.FontWeight.bold, fontSize: 11),
                cellStyle: pw.TextStyle(font: font, fontSize: 9),
                headerDecoration:
                    const pw.BoxDecoration(color: PdfColor.fromInt(0xFFFFF3E0)),
                headers: [
                  'التاريخ',
                  'المزرعة',
                  'العميل',
                  'المدة (ساعة)',
                  'التكلفة (ريال)',
                  'استهلاك الديزل (لتر)',
                ],
                data: [
                  ...filteredIrrigations.take(20).map((irrigation) => [ // أخذ أول 20 تسقية فقط
                        DateFormat('yyyy-MM-dd').format(irrigation.startTime),
                        'مزرعة ${irrigation.farmId}',
                        'عميل ${irrigation.clientId}',
                        irrigation.duration.toStringAsFixed(1),
                        irrigation.cost.toStringAsFixed(2),
                        irrigation.dieselConsumption.toStringAsFixed(2),
                      ]),
                  // صف الإجمالي
                  [
                    '',
                    '',
                    'الإجمالي',
                    totalIrrigationHours.toStringAsFixed(1),
                    totalRevenue.toStringAsFixed(2),
                    totalDieselConsumption.toStringAsFixed(2),
                  ]
                ],
                cellAlignments: {
                  0: pw.Alignment.center,
                  1: pw.Alignment.centerRight,
                  2: pw.Alignment.centerRight,
                  3: pw.Alignment.center,
                  4: pw.Alignment.center,
                  5: pw.Alignment.center,
                },
              ),
              if (filteredIrrigations.length > 20)
                pw.Text('* تم عرض أول 20 تسقية فقط من أصل ${filteredIrrigations.length} تسقية',
                    style: pw.TextStyle(font: font, fontSize: 10, color: PdfColors.grey600)),
            ],

            pw.SizedBox(height: 20),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.end,
              children: [
                pw.Text('توقيع الإدارة: _______________',
                    style: pw.TextStyle(font: font, fontSize: 13)),
              ],
            ),
          ],
        ),
      );

      return pdf;
  }

  // دالة إنشاء تقرير ملخص (إحصائيات فقط)
  Future<pw.Document> _createSummaryPdfDocument() async {
    final pdf = pw.Document();
    // استخدام الخط الافتراضي بدلاً من تحميل ملف خارجي
    final font = pw.Font.helvetica();
    final boldFont = pw.Font.helveticaBold();

    final filteredIrrigations = _getFilteredIrrigations();
    final filteredPayments = _getFilteredPayments();
    final totalRevenue = _calculateFilteredTotalRevenue();
    final totalExpenses = _calculateFilteredTotalExpenses();
    final netProfit = totalRevenue - totalExpenses;

    pdf.addPage(
      pw.Page(
        theme: pw.ThemeData.withFont(base: font, bold: boldFont),
        textDirection: pw.TextDirection.rtl,
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (context) => pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Header
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('التقرير الملخص', style: pw.TextStyle(font: boldFont, fontSize: 24)),
                pw.Text('${DateFormat('yyyy-MM-dd').format(DateTime.now())}', 
                        style: pw.TextStyle(font: font, fontSize: 12)),
              ],
            ),
            pw.SizedBox(height: 20),
            
            // Period
            if (_startDate != null && _endDate != null)
              pw.Text('الفترة: من ${DateFormat('yyyy-MM-dd').format(_startDate!)} إلى ${DateFormat('yyyy-MM-dd').format(_endDate!)}',
                      style: pw.TextStyle(font: font, fontSize: 14)),
            pw.SizedBox(height: 30),

            // Statistics Cards
            pw.Wrap(
              spacing: 20,
              runSpacing: 20,
              children: [
                _buildStatBox('إجمالي الإيرادات', '${totalRevenue.toStringAsFixed(2)} ريال', PdfColors.green, font, boldFont),
                _buildStatBox('إجمالي المصروفات', '${totalExpenses.toStringAsFixed(2)} ريال', PdfColors.red, font, boldFont),
                _buildStatBox('صافي الربح', '${netProfit.toStringAsFixed(2)} ريال', 
                             netProfit >= 0 ? PdfColors.green : PdfColors.red, font, boldFont),
                _buildStatBox('عدد التسقيات', '${filteredIrrigations.length}', PdfColors.blue, font, boldFont),
                _buildStatBox('عدد المدفوعات', '${filteredPayments.length}', PdfColors.orange, font, boldFont),
                _buildStatBox('إجمالي ساعات التشغيل', 
                             '${filteredIrrigations.fold<double>(0, (sum, i) => sum + i.duration).toStringAsFixed(1)} ساعة', 
                             PdfColors.purple, font, boldFont),
              ],
            ),
          ],
        ),
      ),
    );
    return pdf;
  }

  // دالة إنشاء تقرير مقارن (مقارنة بين فترتين)
  Future<pw.Document> _createComparisonPdfDocument() async {
    final pdf = pw.Document();
    // استخدام الخط الافتراضي بدلاً من تحميل ملف خارجي
    final font = pw.Font.helvetica();
    final boldFont = pw.Font.helveticaBold();

    // الفترة الحالية
    final currentIrrigations = _getFilteredIrrigations();
    final currentPayments = _getFilteredPayments();
    final currentRevenue = _calculateFilteredTotalRevenue();
    final currentExpenses = _calculateFilteredTotalExpenses();

    // الفترة السابقة (نفس المدة قبل الفترة الحالية)
    final daysDiff = _endDate!.difference(_startDate!).inDays;
    final previousStart = _startDate!.subtract(Duration(days: daysDiff));
    final previousEnd = _startDate!.subtract(const Duration(days: 1));
    
    final previousIrrigations = _irrigations.where((irrigation) {
      final irrigationDate = irrigation.startTime;
      return irrigationDate.isAfter(previousStart.subtract(const Duration(days: 1))) &&
             irrigationDate.isBefore(previousEnd.add(const Duration(days: 1)));
    }).toList();

    final previousPayments = _payments.where((payment) {
      final paymentDate = payment.paymentDate;
      return paymentDate.isAfter(previousStart.subtract(const Duration(days: 1))) &&
             paymentDate.isBefore(previousEnd.add(const Duration(days: 1)));
    }).toList();

    final previousRevenue = previousIrrigations.fold<double>(0, (sum, i) => sum + i.cost);
    final previousExpenses = previousPayments.fold<double>(0, (sum, p) => sum + p.amount);

    pdf.addPage(
      pw.Page(
        theme: pw.ThemeData.withFont(base: font, bold: boldFont),
        textDirection: pw.TextDirection.rtl,
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(20),
        build: (context) => pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Text('التقرير المقارن', style: pw.TextStyle(font: boldFont, fontSize: 24)),
            pw.SizedBox(height: 10),
            if (_startDate != null && _endDate != null)
              pw.Text('الفترة الحالية: ${DateFormat('yyyy-MM-dd').format(_startDate!)} - ${DateFormat('yyyy-MM-dd').format(_endDate!)}',
                      style: pw.TextStyle(font: font, fontSize: 12)),
            if (_startDate != null && _endDate != null)
              pw.Text('الفترة السابقة: ${DateFormat('yyyy-MM-dd').format(previousStart)} - ${DateFormat('yyyy-MM-dd').format(previousEnd)}',
                      style: pw.TextStyle(font: font, fontSize: 12)),
            pw.SizedBox(height: 20),
            
            // Comparison Table
            pw.TableHelper.fromTextArray(
              cellAlignment: pw.Alignment.center,
              headerStyle: pw.TextStyle(font: boldFont, fontSize: 12),
              cellStyle: pw.TextStyle(font: font, fontSize: 11),
              headers: ['المؤشر', 'الفترة الحالية', 'الفترة السابقة', 'النسبة %'],
              data: [
                ['الإيرادات', '${currentRevenue.toStringAsFixed(2)} ريال', 
                 '${previousRevenue.toStringAsFixed(2)} ريال',
                 _calculatePercentageChange(currentRevenue, previousRevenue)],
                ['المصروفات', '${currentExpenses.toStringAsFixed(2)} ريال',
                 '${previousExpenses.toStringAsFixed(2)} ريال', 
                 _calculatePercentageChange(currentExpenses, previousExpenses)],
                ['عدد التسقيات', '${currentIrrigations.length}', '${previousIrrigations.length}',
                 _calculatePercentageChange(currentIrrigations.length.toDouble(), previousIrrigations.length.toDouble())],
                ['عدد المدفوعات', '${currentPayments.length}', '${previousPayments.length}',
                 _calculatePercentageChange(currentPayments.length.toDouble(), previousPayments.length.toDouble())],
              ],
            ),
          ],
        ),
      ),
    );
    return pdf;
  }

  // دالة مساعدة لحساب نسبة التغيير
  String _calculatePercentageChange(double current, double previous) {
    if (previous == 0) return current > 0 ? '+∞%' : '0%';
    final change = ((current - previous) / previous) * 100;
    return '${change >= 0 ? '+' : ''}${change.toStringAsFixed(1)}%';
  }

  // دالة مساعدة لإنشاء صندوق الإحصائيات في PDF
  pw.Widget _buildStatBox(String title, String value, PdfColor color, pw.Font font, pw.Font boldFont) {
    return pw.Container(
      width: 150,
      height: 80,
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue.shade(0.1),
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: color, width: 1),
      ),
      child: pw.Column(
        mainAxisAlignment: pw.MainAxisAlignment.center,
        children: [
          pw.Text(title, style: pw.TextStyle(font: font, fontSize: 10), textAlign: pw.TextAlign.center),
          pw.SizedBox(height: 8),
          pw.Text(value, style: pw.TextStyle(font: boldFont, fontSize: 12, color: color), textAlign: pw.TextAlign.center),
        ],
      ),
    );
  }

  // دوال تصدير التقارير الثلاثة
  Future<void> _exportSummaryPdf(BuildContext context) async {
    try {
      final pdf = await _createSummaryPdfDocument();
      await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdf.save());
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تصدير التقرير الملخص بنجاح'), backgroundColor: Colors.green),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تصدير التقرير الملخص: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _exportDetailedPdf(BuildContext context) async {
    try {
      final pdf = await _createPdfDocument(); // استخدام التقرير المفصل الحالي
      await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdf.save());
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تصدير التقرير المفصل بنجاح'), backgroundColor: Colors.blue),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تصدير التقرير المفصل: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _exportComparisonPdf(BuildContext context) async {
    try {
      final pdf = await _createComparisonPdfDocument();
      await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => pdf.save());
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تصدير التقرير المقارن بنجاح'), backgroundColor: Colors.orange),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تصدير التقرير المقارن: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  // دالة مشاركة PDF
  Future<void> _sharePdfReport(BuildContext context) async {
    try {
      final pdf = await _createPdfDocument();
      
      await Printing.sharePdf(
        bytes: await pdf.save(),
        filename: 'التقرير_الشامل_${DateTime.now().millisecondsSinceEpoch}.pdf',
      );

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم مشاركة التقرير الشامل بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في مشاركة PDF: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // دالة عرض PDF
  Future<void> _exportToPdf(BuildContext context) async {
    try {
      final pdf = await _createPdfDocument();
      
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
      );

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم عرض التقرير الشامل بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في عرض PDF: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // بطاقة إحصائية موحدة
  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 28),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(title,
                      style: const TextStyle(
                          fontSize: 16, fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  Text(value,
                      style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: color)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
