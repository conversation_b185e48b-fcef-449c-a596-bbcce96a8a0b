import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';


/// خدمة الإشعارات المبسطة
class SimpleNotificationService {
  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();
  static bool _isInitialized = false;

  /// تهيئة خدمة الإشعارات
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // إعدادات Android
      const AndroidInitializationSettings androidSettings =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // إعدادات iOS
      const DarwinInitializationSettings iosSettings =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      // إعدادات عامة
      const InitializationSettings settings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      // تهيئة الإشعارات
      await _notifications.initialize(
        settings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // إنشاء قناة الإشعارات (Android)
      if (Platform.isAndroid) {
        await _createNotificationChannel();
      }

      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة الإشعارات المبسطة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الإشعارات: $e');
    }
  }

  /// إنشاء قناة الإشعارات
  static Future<void> _createNotificationChannel() async {
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'default_channel',
      'الإشعارات العامة',
      description: 'قناة الإشعارات العامة للتطبيق',
      importance: Importance.high,
      playSound: true,
      enableVibration: true,
      enableLights: true,
      showBadge: true,
    );

    final androidImplementation =
        _notifications.resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>();

    await androidImplementation?.createNotificationChannel(channel);
    debugPrint('✅ تم إنشاء قناة الإشعارات');
  }

  /// إرسال إشعار بسيط
  static Future<void> showNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      // إعدادات Android
      const AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
        'default_channel',
        'الإشعارات العامة',
        channelDescription: 'قناة الإشعارات العامة للتطبيق',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
        enableLights: true,
        enableVibration: true,
        playSound: true,
        icon: '@mipmap/ic_launcher',
        color: Colors.blue,
        autoCancel: true,
      );

      // إعدادات iOS
      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      // إعدادات عامة
      const NotificationDetails details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // إنشاء معرف فريد للإشعار
      final notificationId =
          DateTime.now().millisecondsSinceEpoch.remainder(100000);

      // إرسال الإشعار
      await _notifications.show(
        notificationId,
        title,
        body,
        details,
        payload: payload,
      );

      debugPrint('✅ تم إرسال الإشعار: $title');
    } catch (e) {
      debugPrint('❌ خطأ في إرسال الإشعار: $e');
      rethrow;
    }
  }

  /// إرسال إشعار نجاح
  static Future<void> showSuccessNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    await showNotification(
      title: '✅ $title',
      body: body,
      payload: payload,
    );
  }

  /// إرسال إشعار خطأ
  static Future<void> showErrorNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    await showNotification(
      title: '❌ $title',
      body: body,
      payload: payload,
    );
  }

  /// إرسال إشعار تحذير
  static Future<void> showWarningNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    await showNotification(
      title: '⚠️ $title',
      body: body,
      payload: payload,
    );
  }

  /// إرسال إشعار معلومات
  static Future<void> showInfoNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    await showNotification(
      title: 'ℹ️ $title',
      body: body,
      payload: payload,
    );
  }

  /// إرسال إشعار تذكير
  static Future<void> showReminderNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    await showNotification(
      title: '⏰ $title',
      body: body,
      payload: payload,
    );
  }

  /// إلغاء جميع الإشعارات
  static Future<void> cancelAllNotifications() async {
    try {
      await _notifications.cancelAll();
      debugPrint('✅ تم إلغاء جميع الإشعارات');
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء الإشعارات: $e');
    }
  }

  /// معالجة النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('🔔 تم النقر على الإشعار: ${response.payload}');
    // يمكن إضافة منطق إضافي هنا
  }

  /// اختبار الإشعارات
  static Future<void> testNotifications() async {
    try {
      // إشعار معلومات
      await showInfoNotification(
        title: 'اختبار الإشعارات',
        body: 'هذا إشعار معلومات تجريبي',
        payload: 'test_info',
      );

      // انتظار قليل
      await Future.delayed(const Duration(seconds: 2));

      // إشعار نجاح
      await showSuccessNotification(
        title: 'نجاح في الاختبار',
        body: 'تم إرسال الإشعار بنجاح',
        payload: 'test_success',
      );

      // انتظار قليل
      await Future.delayed(const Duration(seconds: 2));

      // إشعار تحذير
      await showWarningNotification(
        title: 'تحذير تجريبي',
        body: 'هذا إشعار تحذير تجريبي',
        payload: 'test_warning',
      );

      // انتظار قليل
      await Future.delayed(const Duration(seconds: 2));

      // إشعار تذكير
      await showReminderNotification(
        title: 'تذكير تجريبي',
        body: 'هذا إشعار تذكير تجريبي',
        payload: 'test_reminder',
      );

      debugPrint('✅ تم إرسال جميع الإشعارات التجريبية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في اختبار الإشعارات: $e');
      rethrow;
    }
  }
}
