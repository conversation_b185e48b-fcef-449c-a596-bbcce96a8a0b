import 'package:flutter/material.dart';
import 'package:untitled/core/services/simple_notification_service.dart';
import 'package:untitled/data/models/client_model.dart';
import 'package:untitled/data/models/irrigation_model.dart';
import 'package:untitled/data/models/payment_model.dart';
import 'package:untitled/data/datasources/client_datasource.dart';
import 'package:untitled/data/datasources/irrigation_datasource.dart';
import 'package:untitled/data/datasources/payment_datasource.dart';

/// خدمة إشعارات العملاء المتكاملة
class ClientNotificationService {
  static final ClientDataSource _clientDataSource = ClientDataSource();
  static final IrrigationDataSource _irrigationDataSource =
      IrrigationDataSource();
  static final PaymentDataSource _paymentDataSource = PaymentDataSource();

  /// إرسال إشعار عند إضافة عميل جديد
  static Future<void> notifyNewClient(ClientModel client) async {
    await SimpleNotificationService.showSuccessNotification(
      title: 'عميل جديد',
      body: 'تم إضافة العميل ${client.name} بنجاح',
    );
  }

  /// إرسال إشعار عند تحديث بيانات العميل
  static Future<void> notifyClientUpdated(ClientModel client) async {
    await SimpleNotificationService.showInfoNotification(
      title: 'تحديث بيانات العميل',
      body: 'تم تحديث بيانات العميل ${client.name}',
    );
  }

  /// إرسال إشعار عند حذف العميل
  static Future<void> notifyClientDeleted(String clientName) async {
    await SimpleNotificationService.showWarningNotification(
      title: 'حذف العميل',
      body: 'تم حذف العميل $clientName',
    );
  }

  /// إرسال إشعار عند إضافة تسقية جديدة
  static Future<void> notifyNewIrrigation(
      IrrigationModel irrigation, ClientModel client) async {
    await SimpleNotificationService.showInfoNotification(
      title: 'تسقية جديدة',
      body:
          'تم إضافة تسقية للعميل ${client.name} - ${irrigation.duration} ساعة',
    );
  }

  /// إرسال إشعار عند إضافة دفعة جديدة
  static Future<void> notifyNewPayment(
      PaymentModel payment, ClientModel client) async {
    await SimpleNotificationService.showSuccessNotification(
      title: 'دفعة جديدة',
      body: 'تم استلام دفعة من العميل ${client.name} - ${payment.amount} ريال',
    );
  }

  /// إرسال إشعار دين متأخر
  static Future<void> notifyOverdueDebt(
      ClientModel client, double debtAmount) async {
    await SimpleNotificationService.showErrorNotification(
      title: 'دين متأخر',
      body:
          'العميل ${client.name} لديه دين متأخر: ${debtAmount.toStringAsFixed(0)} ريال',
    );
  }

  /// إرسال إشعار رصيد إيجابي كبير
  static Future<void> notifyLargeCredit(
      ClientModel client, double creditAmount) async {
    await SimpleNotificationService.showWarningNotification(
      title: 'رصيد إيجابي كبير',
      body:
          'العميل ${client.name} لديه رصيد إيجابي: ${creditAmount.toStringAsFixed(0)} ريال',
    );
  }

  /// إرسال إشعار عدم وجود مدفوعات حديثة
  static Future<void> notifyNoRecentPayments(
      ClientModel client, int daysSinceLastPayment) async {
    await SimpleNotificationService.showReminderNotification(
      title: 'لا توجد مدفوعات حديثة',
      body: 'العميل ${client.name} لم يدفع منذ $daysSinceLastPayment يوم',
    );
  }

  /// إرسال إشعار انتهاء التسقية
  static Future<void> notifyIrrigationCompleted(
      IrrigationModel irrigation, ClientModel client) async {
    await SimpleNotificationService.showInfoNotification(
      title: 'انتهاء التسقية',
      body: 'انتهت تسقية العميل ${client.name} - ${irrigation.duration} ساعة',
    );
  }

  /// إرسال إشعار استهلاك ديزل عالي
  static Future<void> notifyHighDieselConsumption(IrrigationModel irrigation,
      ClientModel client, double consumption) async {
    await SimpleNotificationService.showWarningNotification(
      title: 'استهلاك ديزل عالي',
      body: 'تسقية العميل ${client.name} استهلكت $consumption لتر ديزل',
    );
  }

  /// إرسال إشعار تذكير بالصيانة
  static Future<void> notifyMaintenanceReminder() async {
    await SimpleNotificationService.showReminderNotification(
      title: 'تذكير بالصيانة',
      body: 'حان موعد الصيانة الدورية للمعدات',
    );
  }

  /// إرسال إشعار نسخ احتياطي
  static Future<void> notifyBackupCompleted() async {
    await SimpleNotificationService.showSuccessNotification(
      title: 'نسخ احتياطي',
      body: 'تم إنشاء نسخة احتياطية بنجاح',
    );
  }

  /// إرسال إشعار استعادة البيانات
  static Future<void> notifyDataRestored() async {
    await SimpleNotificationService.showSuccessNotification(
      title: 'استعادة البيانات',
      body: 'تم استعادة البيانات بنجاح',
    );
  }

  /// إرسال إشعار خطأ في النظام
  static Future<void> notifySystemError(String errorMessage) async {
    await SimpleNotificationService.showErrorNotification(
      title: 'خطأ في النظام',
      body: errorMessage,
    );
  }

  /// مراقبة وتحديث حالة العملاء
  static Future<void> monitorClientStatus() async {
    try {
      final clients = await _clientDataSource.getAllClients();

      for (final client in clients) {
        final irrigations =
            await _irrigationDataSource.getIrrigationsByClientId(client.id!);
        final payments =
            await _paymentDataSource.getPaymentsByClientId(client.id!);

        // حساب الرصيد
        final totalCost = irrigations.fold<double>(0, (sum, i) => sum + i.cost);
        final totalPaid = payments.fold<double>(0, (sum, p) => sum + p.amount);
        final balance = totalPaid - totalCost;

        // إشعار دين متأخر
        if (balance < -1000) {
          await notifyOverdueDebt(client, balance.abs());
        }

        // إشعار رصيد إيجابي كبير
        if (balance > 5000) {
          await notifyLargeCredit(client, balance);
        }

        // إشعار عدم وجود مدفوعات حديثة
        if (payments.isNotEmpty) {
          final lastPayment = payments
              .reduce((a, b) => a.createdAt.isAfter(b.createdAt) ? a : b);
          final daysSinceLastPayment =
              DateTime.now().difference(lastPayment.createdAt).inDays;

          if (daysSinceLastPayment > 30 && balance < 0) {
            await notifyNoRecentPayments(client, daysSinceLastPayment);
          }
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في مراقبة حالة العملاء: $e');
    }
  }

  /// مراقبة استهلاك الديزل
  static Future<void> monitorDieselConsumption() async {
    try {
      final irrigations = await _irrigationDataSource.getAllIrrigations();
      final recentIrrigations = irrigations
          .where((i) => DateTime.now().difference(i.createdAt).inDays <= 7)
          .toList();

      if (recentIrrigations.isNotEmpty) {
        final avgConsumption = recentIrrigations.fold<double>(
                0, (sum, i) => sum + i.dieselConsumption) /
            recentIrrigations.length;

        for (final irrigation in recentIrrigations) {
          if (irrigation.dieselConsumption > avgConsumption * 1.5) {
            final client =
                await _clientDataSource.getClientById(irrigation.clientId);
            if (client != null) {
              await notifyHighDieselConsumption(
                  irrigation, client, irrigation.dieselConsumption);
            }
          }
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في مراقبة استهلاك الديزل: $e');
    }
  }

  /// إرسال تقرير يومي
  static Future<void> sendDailyReport() async {
    try {
      // final clients = await _clientDataSource.getAllClients();
      final irrigations = await _irrigationDataSource.getAllIrrigations();
      final payments = await _paymentDataSource.getAllPayments();

      final today = DateTime.now();
      final todayIrrigations = irrigations
          .where((i) =>
              i.createdAt.day == today.day &&
              i.createdAt.month == today.month &&
              i.createdAt.year == today.year)
          .toList();

      final todayPayments = payments
          .where((p) =>
              p.createdAt.day == today.day &&
              p.createdAt.month == today.month &&
              p.createdAt.year == today.year)
          .toList();

      final totalTodayCost =
          todayIrrigations.fold<double>(0, (sum, i) => sum + i.cost);
      final totalTodayPayments =
          todayPayments.fold<double>(0, (sum, p) => sum + p.amount);

      await SimpleNotificationService.showInfoNotification(
        title: 'التقرير اليومي',
        body:
            'اليوم: ${todayIrrigations.length} تسقية، ${todayPayments.length} دفعة، إجمالي: ${totalTodayPayments - totalTodayCost} ريال',
      );
    } catch (e) {
      debugPrint('❌ خطأ في إرسال التقرير اليومي: $e');
    }
  }

  /// إرسال إشعار تجريبي محسن
  static Future<void> sendTestNotification() async {
    try {
      // إشعار تجريبي مع تفاصيل - يظهر كإشعار نظام
      await SimpleNotificationService.showInfoNotification(
        title: '🔔 اختبار إشعارات النظام',
        body:
            'هذا إشعار تجريبي يظهر كإشعار نظام خارجي\n\n✅ يظهر في شريط الإشعارات\n✅ صوت واهتزاز\n✅ يمكن النقر عليه',
      );

      // إشعار تحذير تجريبي - يظهر كإشعار مهم
      await Future.delayed(const Duration(seconds: 3));
      await SimpleNotificationService.showWarningNotification(
        title: '⚠️ تحذير مهم',
        body: 'هذا إشعار تحذير مهم يظهر كإشعار نظام مع أولوية عالية',
      );

      // إشعار نجاح تجريبي - يظهر كإشعار عادي
      await Future.delayed(const Duration(seconds: 3));
      await SimpleNotificationService.showSuccessNotification(
        title: '✅ نجاح في العملية',
        body: 'تم إنجاز العملية بنجاح - إشعار نظام عادي',
      );

      // إشعار خطأ تجريبي - يظهر كإشعار نظام مهم
      await Future.delayed(const Duration(seconds: 3));
      await SimpleNotificationService.showErrorNotification(
        title: '❌ خطأ في النظام',
        body: 'حدث خطأ في النظام - إشعار نظام مهم يتطلب الانتباه',
      );

      debugPrint('✅ تم إرسال إشعارات النظام التجريبية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في إرسال إشعارات النظام التجريبية: $e');
      rethrow;
    }
  }

  /// إرسال إشعارات تلقائية بناءً على البيانات
  static Future<void> sendAutomaticNotifications() async {
    try {
      // مراقبة حالة العملاء
      await monitorClientStatus();

      // مراقبة استهلاك الديزل
      await monitorDieselConsumption();

      // إرسال التقرير اليومي (إذا كان الوقت مناسب)
      final now = DateTime.now();
      if (now.hour == 20 && now.minute == 0) {
        // الساعة 8 مساءً
        await sendDailyReport();
      }

      debugPrint('✅ تم إرسال الإشعارات التلقائية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في إرسال الإشعارات التلقائية: $e');
    }
  }
}
