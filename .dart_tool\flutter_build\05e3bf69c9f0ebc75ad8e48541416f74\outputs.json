["C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/app_logo.png", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/README.md", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/Cairo-VariableFont_slnt,wght.ttf", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/Cairo.zip", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/OFL.txt", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/README.txt", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/static/Cairo-Regular.ttf", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/sounds/notification_sound.mp3", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/sounds/README.md", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/fonts/static/Cairo-Bold.ttf", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "C:\\Users\\<USER>\\StudioProjects\\untitled\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]